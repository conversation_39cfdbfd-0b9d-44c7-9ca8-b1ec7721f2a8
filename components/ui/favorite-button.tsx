'use client'

import { useState, useEffect } from 'react'
import { Button } from "@/components/ui/button"
import { Heart } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { 
  addFacultyToFavorites, 
  removeFacultyFromFavorites,
  isFacultyFavorite
} from "@/lib/faculty-utils"
import { FacultyMember } from "@/lib/data/faculty"

interface FavoriteButtonProps {
  faculty: FacultyMember
  className?: string
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  showText?: boolean
}

export function FavoriteButton({
  faculty,
  className,
  variant = "outline",
  size = "sm",
  showText = true
}: FavoriteButtonProps) {
  const [isFavorite, setIsFavorite] = useState(false)
  const { toast } = useToast()
  
  // Check if faculty is already a favorite on mount
  useEffect(() => {
    setIsFavorite(isFacultyFavorite(faculty.id))
  }, [faculty.id])
  
  const handleToggleFavorite = () => {
    if (isFavorite) {
      const removed = removeFacultyFromFavorites(faculty.id)
      if (removed) {
        setIsFavorite(false)
        toast({
          title: "Removed from favorites",
          description: `${faculty.name} has been removed from your favorites.`
        })
      }
    } else {
      const added = addFacultyToFavorites(faculty)
      if (added) {
        setIsFavorite(true)
        toast({
          title: "Added to favorites",
          description: `${faculty.name} has been added to your favorites.`
        })
      }
    }
  }
  
  return (
    <Button
      variant={isFavorite ? "default" : variant}
      size={size}
      className={className}
      onClick={handleToggleFavorite}
      aria-label={isFavorite ? `Remove ${faculty.name} from favorites` : `Add ${faculty.name} to favorites`}
      title={isFavorite ? "Remove from favorites" : "Add to favorites"}
    >
      <Heart 
        className={`h-4 w-4 ${showText ? 'mr-2' : ''} ${isFavorite ? 'fill-current' : ''}`} 
      />
      {showText && (
        <span>{isFavorite ? 'Favorited' : 'Favorite'}</span>
      )}
    </Button>
  )
} 