'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { getFavoriteFaculty, removeFacultyFromFavorites, StoredFaculty } from "@/lib/faculty-utils"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Heart, Trash2 } from "lucide-react"
import { LazyImage } from "@/components/ui/lazy-image"
import { useToast } from "@/components/ui/use-toast"

interface FacultyFavoritesProps {
  className?: string
  showRemoveButton?: boolean
}

export function FacultyFavorites({
  className,
  showRemoveButton = true
}: FacultyFavoritesProps) {
  const [favorites, setFavorites] = useState<StoredFaculty[]>([])
  const { toast } = useToast()
  
  useEffect(() => {
    // Get favorites on client side
    const storedFavorites = getFavoriteFaculty()
    setFavorites(storedFavorites)
    
    // Setup event listener to update when favorites change
    const handleStorageChange = () => {
      const updatedFavorites = getFavoriteFaculty()
      setFavorites(updatedFavorites)
    }
    
    window.addEventListener('storage', handleStorageChange)
    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])
  
  const handleRemove = (faculty: StoredFaculty, event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()
    
    const removed = removeFacultyFromFavorites(faculty.id)
    if (removed) {
      setFavorites(prev => prev.filter(f => f.id !== faculty.id))
      toast({
        title: "Removed from favorites",
        description: `${faculty.name} has been removed from your favorites.`
      })
      
      // Dispatch storage event to update other components
      window.dispatchEvent(new Event('storage'))
    }
  }
  
  if (favorites.length === 0) {
    return (
      <div className={className}>
        <div className="flex items-center gap-2 mb-3">
          <Heart className="h-4 w-4 text-primary" />
          <h2 className="text-base font-medium">Favorites</h2>
        </div>
        
        <div className="text-center py-6 border rounded-lg bg-muted/20">
          <Heart className="h-10 w-10 mx-auto text-muted-foreground/50 mb-2" />
          <p className="text-sm text-muted-foreground">
            You haven't added any faculty to your favorites yet.
          </p>
        </div>
      </div>
    )
  }
  
  return (
    <div className={className}>
      <div className="flex items-center gap-2 mb-3">
        <Heart className="h-4 w-4 text-primary" />
        <h2 className="text-base font-medium">Favorites</h2>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
        {favorites.map((faculty) => (
          <Link key={faculty.id} href={`/faculty/${faculty.id}`}>
            <Card className="h-full hover:shadow-sm transition-shadow relative">
              {showRemoveButton && (
                <Button 
                  variant="destructive" 
                  size="icon" 
                  className="absolute right-1.5 top-1.5 h-6 w-6 z-10 opacity-80 hover:opacity-100"
                  onClick={(e) => handleRemove(faculty, e)}
                  aria-label={`Remove ${faculty.name} from favorites`}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
              <div className="aspect-square w-full overflow-hidden bg-muted/50">
                <LazyImage
                  src={faculty.imageUrl}
                  alt={`${faculty.name}`}
                  aspectRatio="aspect-square"
                />
              </div>
              <CardContent className="p-2">
                <h3 className="text-xs font-medium truncate">{faculty.name}</h3>
                <p className="text-xs text-muted-foreground truncate mt-0.5">
                  {faculty.title}
                </p>
                <p className="text-[10px] text-muted-foreground mt-1 truncate">
                  {faculty.department}
                </p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  )
} 