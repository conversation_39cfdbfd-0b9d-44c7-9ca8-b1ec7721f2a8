'use client'

import React, { useState, useEffect } from 'react'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>ist, 
  Ta<PERSON>Trigger 
} from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import { 
  FileText, 
  Link as LinkIcon, 
  ExternalLink, 
  Calendar,
  Users,
  BarChart,
  Search
} from "lucide-react"

// In a real app, these would come from a Google Scholar API
export interface Publication {
  id: string
  title: string
  authors: string[]
  journal: string
  year: number
  citationCount: number
  link: string
  abstract?: string
  tags?: string[]
}

interface ScholarlyPublicationsProps {
  initialPublications: Publication[]
  scholarId?: string // Google Scholar ID
  isLoading?: boolean
}

export function ScholarlyPublications({ 
  initialPublications, 
  scholarId,
  isLoading = false
}: ScholarlyPublicationsProps) {
  const [publications, setPublications] = useState<Publication[]>(initialPublications)
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState<'all' | 'journal' | 'conference' | 'recent'>('all')
  const [loading, setLoading] = useState(isLoading)
  
  // For a real implementation, this would fetch from Google Scholar API
  useEffect(() => {
    if (scholarId) {
      // Simulate API loading
      setLoading(true)
      
      // Mock API call with timeout
      const timer = setTimeout(() => {
        // In a real app, fetch data from API here
        // For now, just use initial publications
        setPublications(initialPublications)
        setLoading(false)
      }, 1500)
      
      return () => clearTimeout(timer)
    }
  }, [scholarId, initialPublications])
  
  // Filter publications based on search and active tab
  const filteredPublications = publications.filter(pub => {
    const matchesSearch = searchTerm === '' || 
      pub.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pub.authors.some(author => author.toLowerCase().includes(searchTerm.toLowerCase())) ||
      pub.journal.toLowerCase().includes(searchTerm.toLowerCase())
    
    let matchesTab = true
    if (activeTab === 'recent') {
      matchesTab = pub.year >= new Date().getFullYear() - 3 // Last 3 years
    } else if (activeTab === 'journal') {
      matchesTab = !pub.journal.toLowerCase().includes('conference') &&
                   !pub.journal.toLowerCase().includes('proc.')
    } else if (activeTab === 'conference') {
      matchesTab = pub.journal.toLowerCase().includes('conference') ||
                   pub.journal.toLowerCase().includes('proc.')
    }
    
    return matchesSearch && matchesTab
  })
  
  // Sort by citation count (descending)
  const sortedPublications = [...filteredPublications].sort((a, b) => 
    b.citationCount - a.citationCount
  )
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <h2 className="text-2xl font-semibold">Publications</h2>
        
        {scholarId && (
          <a 
            href={`https://scholar.google.com/citations?user=${scholarId}`}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-1 text-sm text-primary"
          >
            <ExternalLink className="h-4 w-4" />
            View on Google Scholar
          </a>
        )}
      </div>
      
      <div className="flex flex-col md:flex-row gap-4 md:items-center">
        <div className="relative md:w-80">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search publications..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Tabs value={activeTab} onValueChange={value => setActiveTab(value as any)} className="md:flex-1">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="journal">Journal</TabsTrigger>
            <TabsTrigger value="conference">Conference</TabsTrigger>
            <TabsTrigger value="recent">Recent</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      {loading ? (
        <div className="space-y-4">
          {[1, 2, 3].map(i => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="space-y-3">
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <div className="flex gap-2 pt-2">
                    <Skeleton className="h-9 w-20" />
                    <Skeleton className="h-9 w-20" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <>
          {sortedPublications.length > 0 ? (
            <div className="space-y-4">
              {sortedPublications.map((pub) => (
                <Card key={pub.id} className="overflow-hidden hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <h3 className="font-medium text-lg mb-2">{pub.title}</h3>
                    
                    <div className="flex flex-wrap gap-x-4 gap-y-2 mb-3 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Users className="h-3.5 w-3.5" />
                        {pub.authors.join(', ')}
                      </span>
                      <span className="flex items-center gap-1">
                        <FileText className="h-3.5 w-3.5" />
                        {pub.journal}
                      </span>
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3.5 w-3.5" />
                        {pub.year}
                      </span>
                      <span className="flex items-center gap-1">
                        <BarChart className="h-3.5 w-3.5" />
                        {pub.citationCount} citations
                      </span>
                    </div>
                    
                    {pub.abstract && (
                      <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                        {pub.abstract}
                      </p>
                    )}
                    
                    <div className="flex flex-wrap gap-2 mt-3">
                      <a href={pub.link} target="_blank" rel="noopener noreferrer">
                        <Button variant="outline" size="sm">
                          <ExternalLink className="mr-1 h-3.5 w-3.5" />
                          View Publication
                        </Button>
                      </a>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 border rounded-lg bg-muted/20">
              <p className="text-muted-foreground mb-2">No publications found.</p>
              {searchTerm && (
                <p className="text-sm text-muted-foreground">
                  Try adjusting your search or filter criteria.
                </p>
              )}
            </div>
          )}
        </>
      )}
    </div>
  )
} 