'use client'

import Link from "next/link"
import { <PERSON>u } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, She<PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { useScrollHeader } from "@/components/scroll-header" // Assuming this path is correct
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { ChevronDown } from "lucide-react"

export default function Header() {
  const scrolled = useScrollHeader()

  return (
    <header
      className={`sticky top-0 z-50 w-full border-b transition-all duration-300 ${
        scrolled
          ? "border-border/40 bg-background/95 backdrop-blur-md"
          : "border-transparent bg-transparent"
      }`}
      role="banner"
      aria-label="Site header"
    >
      <div className="flex h-28 items-center justify-between px-4 md:px-6">
        <div className="flex items-center pl-4 group">
          <Link href="/">
            <img
              src="/UC_logo.svg"
              alt="Ullens College Logo"
              className="h-24 w-24 transition-transform duration-300 group-hover:scale-110"
            />
          </Link>
        </div>
        <nav className="hidden md:flex items-center gap-8" role="navigation" aria-label="Main navigation">
          <Link href="/" className="header-link text-sm font-medium py-1">
            Home
          </Link>

          <DropdownMenu>
            <DropdownMenuTrigger className="header-link text-sm font-medium py-1 flex items-center gap-1">
              Schools <ChevronDown className="h-3.5 w-3.5" />
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem>
                <Link href="/schools/computer-science" className="w-full">Computer Science</Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link href="/schools/agriculture-climate" className="w-full">Agriculture & Climate</Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link href="/schools/business" className="w-full">Business</Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link href="/schools/education" className="w-full">Education</Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link href="/programs/compare" className="w-full">Compare Programs</Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          <DropdownMenu>
            <DropdownMenuTrigger className="header-link text-sm font-medium py-1 flex items-center gap-1">
              Research <ChevronDown className="h-3.5 w-3.5" />
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem>
                <Link href="/research/center" className="w-full">Center of Interdisciplinary Computation</Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          <Link href="/partnerships" className="header-link text-sm font-medium py-1">
            Partnerships
          </Link>
          
          <Link href="/values" className="header-link text-sm font-medium py-1">
            Values
          </Link>
          
          <Button className="shadow-sm transition-all hover:shadow-md hover:scale-105 duration-300">
            Apply Now
          </Button>
        </nav>
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="outline" size="icon" className="md:hidden border-none">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="right" className="border-l-crimson/20">
            <div className="flex flex-col gap-6 pt-10">
              <div className="flex items-center justify-center mb-8">
                <Link href="/">
                  <img src="/UC_logo.svg" alt="Ullens College Logo" className="h-24 w-24" />
                </Link>
              </div>
              <Link href="/" className="nav-link text-base font-medium py-2 border-b border-border/30">
                Home
              </Link>
              <Link href="/schools/computer-science" className="nav-link text-base font-medium py-2 border-b border-border/30">
                Computer Science
              </Link>
              <Link href="/schools/agriculture-climate" className="nav-link text-base font-medium py-2 border-b border-border/30">
                Agriculture & Climate
              </Link>
              <Link href="/schools/business" className="nav-link text-base font-medium py-2 border-b border-border/30">
                Business
              </Link>
              <Link href="/schools/education" className="nav-link text-base font-medium py-2 border-b border-border/30">
                Education
              </Link>
              <Link href="/programs/compare" className="nav-link text-base font-medium py-2 border-b border-border/30">
                Compare Programs
              </Link>
              <Link href="/research/center" className="nav-link text-base font-medium py-2 border-b border-border/30">
                Center of Interdisciplinary Computation
              </Link>
              <Link href="/partnerships" className="nav-link text-base font-medium py-2 border-b border-border/30">
                Partnerships
              </Link>
              <Link href="/values" className="nav-link text-base font-medium py-2 border-b border-border/30">
                Values
              </Link>
              <Button className="mt-4 w-full shadow-sm">Apply Now</Button>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </header>
  )
} 