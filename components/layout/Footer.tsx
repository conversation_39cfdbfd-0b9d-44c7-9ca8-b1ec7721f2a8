import Link from "next/link"

export default function Footer() {
  return (
    <footer className="w-full border-t border-border/30 py-12 md:py-16 bg-light" role="contentinfo" aria-label="Site footer">
      <div className="px-4 md:px-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 md:gap-12">
          <div className="space-y-4">
            <div className="flex items-center pl-4">
              <img src="/UC_logo.svg" alt="Ullens College Logo" className="h-16 w-16" />
            </div>
            <p className="text-muted-foreground">
              Preparing students for success in today's rapidly evolving world.
            </p>
          </div>

          <div className="space-y-4">
            <h4 className="text-base font-semibold">Programs</h4>
            <ul className="space-y-2">
              <li><Link href="#computer-science" className="text-muted-foreground hover:text-crimson transition-colors">Computer Science</Link></li>
              <li><Link href="#agriculture" className="text-muted-foreground hover:text-crimson transition-colors">Agriculture & Climate</Link></li>
              <li><Link href="#business" className="text-muted-foreground hover:text-crimson transition-colors">Business</Link></li>
              <li><Link href="#education" className="text-muted-foreground hover:text-crimson transition-colors">Education</Link></li>
            </ul>
          </div>

          <div className="space-y-4">
            <h4 className="text-base font-semibold">Resources</h4>
            <ul className="space-y-2">
              <li><Link href="#" className="text-muted-foreground hover:text-crimson transition-colors">Campus Life</Link></li>
              <li><Link href="#" className="text-muted-foreground hover:text-crimson transition-colors">Admissions</Link></li>
              <li><Link href="#" className="text-muted-foreground hover:text-crimson transition-colors">Financial Aid</Link></li>
              <li><Link href="#" className="text-muted-foreground hover:text-crimson transition-colors">Career Services</Link></li>
            </ul>
          </div>

          <div className="space-y-4">
            <h4 className="text-base font-semibold">Connect</h4>
            <ul className="space-y-2">
              <li><Link href="#" className="text-muted-foreground hover:text-crimson transition-colors">Contact Us</Link></li>
              <li><Link href="#" className="text-muted-foreground hover:text-crimson transition-colors">Visit Campus</Link></li>
              <li><Link href="#" className="text-muted-foreground hover:text-crimson transition-colors">News & Events</Link></li>
              <li><Link href="#" className="text-muted-foreground hover:text-crimson transition-colors">Alumni</Link></li>
            </ul>
          </div>
        </div>

        <div className="flex flex-col md:flex-row items-center justify-between gap-4 mt-12 pt-6 border-t border-border/30">
          <p className="text-sm text-muted-foreground">© 2025 Ullens College. All rights reserved.</p>
          <div className="flex gap-6">
            <Link href="#" className="text-sm text-muted-foreground hover:text-crimson transition-colors">
              Privacy Policy
            </Link>
            <Link href="#" className="text-sm text-muted-foreground hover:text-crimson transition-colors">
              Terms of Service
            </Link>
            <Link href="#" className="text-sm text-muted-foreground hover:text-crimson transition-colors">
              Accessibility
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
} 