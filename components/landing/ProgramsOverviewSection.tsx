import ProgramOverviewCard from "./ProgramOverviewCard"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Lock, Leaf, Building, BookOpen } from "lucide-react"

const programsData = [
  {
    title: "Computer Science",
    description: "Technology & Innovation",
    details: "Develop cutting-edge skills in programming, AI, cybersecurity, and more.",
    icon: <Lock className="h-6 w-6 text-crimson" />,
    themeColor: "crimson",
  },
  {
    title: "Agriculture",
    description: "Sustainable Practices",
    details: "Learn sustainable farming techniques and environmental management.",
    icon: <Leaf className="h-6 w-6 text-green-600" />,
    themeColor: "green-600",
  },
  {
    title: "Business",
    description: "Leadership & Management",
    details: "Develop business acumen and leadership skills for the global marketplace.",
    icon: <Building className="h-6 w-6 text-gold" />,
    themeColor: "gold",
  },
  {
    title: "Education",
    description: "Teaching & Learning",
    details: "Prepare for a rewarding career shaping the minds of future generations.",
    icon: <BookOpen className="h-6 w-6 text-blue-500" />,
    themeColor: "blue-500",
  },
];

export default function ProgramsOverviewSection() {
  return (
    <section id="programs" className="w-full py-20 md:py-28 lg:py-32 relative overflow-hidden">
      {/* Abstract geometric shapes */}
      <div className="absolute inset-0 overflow-hidden opacity-10">
        <div className="absolute top-1/4 right-0 w-72 h-72 bg-gold rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-1/4 w-64 h-64 bg-crimson/70 rounded-full blur-3xl"></div>
      </div>

      <div className="px-4 md:px-6 relative">
        <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
          <div className="inline-flex items-center px-3 py-1 rounded-full bg-gold/10 text-gold text-sm font-medium">
            Our Schools
          </div>
          <div className="space-y-4">
            <h2 className="heading-lg">
              Discover Your Path to Success
            </h2>
            <p className="text-muted-foreground text-lg leading-relaxed">
              Explore our diverse range of schools designed to prepare you for a successful career in your chosen field.
            </p>
          </div>
        </div>

        {/* Horizontal scrolling container for mobile */}
        <div className="relative pb-4">
          <div className="flex overflow-x-auto pb-4 snap-x snap-mandatory scrollbar-hide">
            {programsData.map((program) => (
              <ProgramOverviewCard
                key={program.title}
                title={program.title}
                description={program.description}
                details={program.details}
                icon={program.icon}
                themeColor={program.themeColor}
              />
            ))}
          </div>

          {/* Scroll indicator */}
          <div className="mt-4 flex flex-col items-center gap-2 md:hidden">
            <div className="flex gap-1">
              <div className="h-1.5 w-10 rounded-full bg-gold/40"></div>
              <div className="h-1.5 w-3 rounded-full bg-gold"></div>
              <div className="h-1.5 w-3 rounded-full bg-gold/40"></div>
            </div>
            {/* Consider if LoadingSpinner is still needed here or how it should be triggered */}
            {/* <LoadingSpinner size="sm" className="mt-2" /> */}
          </div>
        </div>
      </div>
    </section>
  )
} 