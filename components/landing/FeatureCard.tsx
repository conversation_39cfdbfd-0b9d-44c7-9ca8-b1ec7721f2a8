import { cn } from "@/lib/utils";

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  themeColor?: 'crimson' | 'gold'; 
}

export default function FeatureCard({ icon, title, description, themeColor = 'crimson' }: FeatureCardProps) {
  const iconBgClass = {
    'crimson': 'bg-crimson/10',
    'gold': 'bg-gold/10',
  }[themeColor] || 'bg-crimson/10';

  return (
    <div className="flex flex-col items-center text-center p-6 rounded-xl bg-white shadow-sm">
      <div className={cn("w-12 h-12 rounded-full flex items-center justify-center mb-4", iconBgClass)}>
        {icon}
      </div>
      <h3 className="text-xl font-bold mb-2">{title}</h3>
      <p className="text-muted-foreground">{description}</p>
    </div>
  );
} 