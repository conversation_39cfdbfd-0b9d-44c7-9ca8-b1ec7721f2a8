interface GalleryItemProps {
  imageUrl: string;
  altText: string;
  title: string;
  description: string;
  parallax?: boolean; // To enable parallax effect if needed via style prop
}

export default function GalleryItem({ imageUrl, altText, title, description, parallax }: GalleryItemProps) {
  const parallaxStyle = parallax ? {
    transform: 'translateY(-5%)', // Initial transform, hook will update this
    transition: 'transform 0.5s ease-out'
  } : {};

  return (
    <div className="flex-shrink-0 w-full md:w-2/3 lg:w-1/2 snap-center relative">
      <div className="relative h-[50vh] md:h-[60vh] lg:h-[70vh] overflow-hidden mx-1">
        <div className="absolute inset-0 bg-gradient-to-t from-dark/60 to-transparent z-10"></div>
        {imageUrl && (
          <img
            src={imageUrl || undefined}
            alt={altText}
            className={`w-full h-full object-cover transform scale-110 ${parallax ? 'motion-safe:parallax' : ''}`}
            style={parallaxStyle}
          />
        )}
        <div className="absolute bottom-0 left-0 right-0 p-6 z-20 transform translate-y-6 opacity-0 transition-all duration-500 hover:translate-y-0 hover:opacity-100">
          <h3 className="text-2xl font-bold text-white">{title}</h3>
          <p className="text-white/90 mt-2">
            {description}
          </p>
        </div>
      </div>
    </div>
  );
} 