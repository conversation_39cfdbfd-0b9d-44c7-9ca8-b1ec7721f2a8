import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { ArrowRight } from "lucide-react"

interface ProgramOverviewCardProps {
  title: string;
  description: string;
  details: string;
  icon: React.ReactNode;
  themeColor?: string; // e.g., 'crimson', 'green-600', 'gold', 'blue-500'
}

export default function ProgramOverviewCard({ title, description, details, icon, themeColor = 'crimson' }: ProgramOverviewCardProps) {
  // Determine icon color class based on themeColor
  const iconColorClass = {
    'crimson': 'text-crimson',
    'green-600': 'text-green-600',
    'gold': 'text-gold',
    'blue-500': 'text-blue-500',
  }[themeColor] || 'text-crimson';

  const bgColorClass = {
    'crimson': 'bg-crimson/10',
    'green-600': 'bg-green-600/10',
    'gold': 'bg-gold/10',
    'blue-500': 'bg-blue-500/10',
  }[themeColor] || 'bg-crimson/10';

  return (
    <div className="flex-shrink-0 w-[85%] sm:w-[45%] md:w-[30%] lg:w-[22%] snap-start mx-2">
      <Card className="card-hover border-0 bg-light shadow-sm h-full">
        <CardHeader className="pb-2">
          <div className={`w-12 h-12 rounded-full ${bgColorClass} flex items-center justify-center mb-3`}>
            {icon} {/* Render the passed icon directly */}
          </div>
          <CardTitle className="text-xl">{title}</CardTitle>
          <CardDescription className="text-muted-foreground/80">{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground leading-relaxed mb-4">
            {details}
          </p>
          <div className="flex items-center text-sm text-gold font-medium">
            <span>Learn more</span>
            <ArrowRight className="ml-1 h-4 w-4" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 