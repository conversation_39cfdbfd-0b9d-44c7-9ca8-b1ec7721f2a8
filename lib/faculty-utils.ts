'use client'

import { FacultyMember } from "./data/faculty"

// Type definitions for local storage data
export interface StoredFaculty {
  id: string
  name: string
  title: string
  department: string
  imageUrl: string
  timestamp: number
}

// Constants for localStorage keys
const FAVORITES_KEY = 'faculty-favorites'
const RECENTLY_VIEWED_KEY = 'recently-viewed-faculty'
const MAX_RECENT = 10

// Get favorite faculty from localStorage
export function getFavoriteFaculty(): StoredFaculty[] {
  if (typeof window === 'undefined') return []
  
  try {
    const stored = localStorage.getItem(FAVORITES_KEY)
    return stored ? JSON.parse(stored) : []
  } catch (error) {
    console.error('Error getting favorite faculty:', error)
    return []
  }
}

// Add faculty to favorites
export function addFacultyToFavorites(faculty: FacultyMember): boolean {
  if (typeof window === 'undefined') return false
  
  try {
    const favorites = getFavoriteFaculty()
    // Check if already in favorites
    if (favorites.some(fav => fav.id === faculty.id)) {
      return false
    }
    
    // Add to favorites with limited data
    const storedFaculty: StoredFaculty = {
      id: faculty.id,
      name: faculty.name,
      title: faculty.title,
      department: faculty.department,
      imageUrl: faculty.imageUrl,
      timestamp: Date.now()
    }
    
    favorites.push(storedFaculty)
    localStorage.setItem(FAVORITES_KEY, JSON.stringify(favorites))
    return true
  } catch (error) {
    console.error('Error adding faculty to favorites:', error)
    return false
  }
}

// Remove faculty from favorites
export function removeFacultyFromFavorites(facultyId: string): boolean {
  if (typeof window === 'undefined') return false
  
  try {
    const favorites = getFavoriteFaculty()
    const newFavorites = favorites.filter(fav => fav.id !== facultyId)
    
    if (newFavorites.length === favorites.length) {
      return false // Nothing was removed
    }
    
    localStorage.setItem(FAVORITES_KEY, JSON.stringify(newFavorites))
    return true
  } catch (error) {
    console.error('Error removing faculty from favorites:', error)
    return false
  }
}

// Check if faculty is in favorites
export function isFacultyFavorite(facultyId: string): boolean {
  if (typeof window === 'undefined') return false
  
  try {
    const favorites = getFavoriteFaculty()
    return favorites.some(fav => fav.id === facultyId)
  } catch (error) {
    console.error('Error checking if faculty is favorite:', error)
    return false
  }
}

// Get recently viewed faculty
export function getRecentlyViewedFaculty(): StoredFaculty[] {
  if (typeof window === 'undefined') return []
  
  try {
    const stored = localStorage.getItem(RECENTLY_VIEWED_KEY)
    return stored ? JSON.parse(stored) : []
  } catch (error) {
    console.error('Error getting recently viewed faculty:', error)
    return []
  }
}

// Add faculty to recently viewed
export function addFacultyToRecentlyViewed(faculty: FacultyMember): void {
  if (typeof window === 'undefined') return
  
  try {
    const recentlyViewed = getRecentlyViewedFaculty()
    
    // Create the faculty item to store
    const storedFaculty: StoredFaculty = {
      id: faculty.id,
      name: faculty.name,
      title: faculty.title,
      department: faculty.department,
      imageUrl: faculty.imageUrl,
      timestamp: Date.now()
    }
    
    // Remove if already in list to avoid duplicates
    const newList = recentlyViewed.filter(fac => fac.id !== faculty.id)
    
    // Add to front of list
    newList.unshift(storedFaculty)
    
    // Limit to MAX_RECENT items
    if (newList.length > MAX_RECENT) {
      newList.pop()
    }
    
    localStorage.setItem(RECENTLY_VIEWED_KEY, JSON.stringify(newList))
  } catch (error) {
    console.error('Error adding faculty to recently viewed:', error)
  }
}

// Calculate profile completeness as a percentage
export function calculateProfileCompleteness(faculty: FacultyMember): number {
  const requiredFields = [
    'id', 'name', 'title', 'department', 'email', 
    'website', 'bio', 'imageUrl'
  ]
  
  const optionalFields = [
    'education', 'research', 'publications', 'courses', 
    'officeHours', 'office', 'timeline', 'cvDocuments', 
    'scholarlyPublications', 'upcomingClasses', 
    'scheduledOfficeHours', 'researchProjects'
  ]
  
  // Check required fields (each worth 10%)
  const requiredScore = requiredFields.reduce((score, field) => {
    return score + (faculty[field as keyof FacultyMember] ? 10 : 0)
  }, 0)
  
  // Check array fields (each worth 5% if has at least one item)
  const arrayScore = optionalFields.reduce((score, field) => {
    const value = faculty[field as keyof FacultyMember]
    if (Array.isArray(value) && value.length > 0) {
      return score + 5
    }
    return score + (value ? 5 : 0)
  }, 0)
  
  // Calculate total (cap at 100%)
  const total = Math.min(100, requiredScore + arrayScore)
  return total
} 