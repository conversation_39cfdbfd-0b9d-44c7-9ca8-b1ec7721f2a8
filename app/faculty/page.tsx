'use client'

import { useState, useMemo, useEffect } from 'react'
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import Link from "next/link"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, Mail, Globe, BookOpen, Search } from "lucide-react"
import { FacultyFilters } from "@/components/ui/faculty-filters"
import { FacultyOrgChart } from "@/components/ui/faculty-org-chart"
import { FacultyResearchNetwork } from "@/components/ui/faculty-research-network"
import { LazyImage } from "@/components/ui/lazy-image"

// Import faculty data and helper functions
import { 
  facultyData, 
  researchAreas, 
  getUniqueDepartments, 
  getUniqueResearchAreas,
  searchFaculty,
  filterFacultyByDepartment,
  filterFacultyByResearchArea 
} from "@/lib/data/faculty"

// Import recently viewed and favorites components
import { RecentlyViewedFaculty } from "@/components/ui/recently-viewed-faculty"
import { FacultyFavorites } from "@/components/ui/faculty-favorites"
import { Breadcrumb } from "@/components/ui/breadcrumb"

export default function FacultyDirectoryPage() {
  // State for filters and view mode
  const [viewMode, setViewMode] = useState<'grid' | 'org' | 'research'>('grid')
  const [filters, setFilters] = useState({
    search: '',
    department: '',
    researchArea: '',
    expertiseFilter: [] as string[],
    viewMode: 'grid' as 'grid' | 'org' | 'research'
  })
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([])
  const [showRecent, setShowRecent] = useState(false)
  const [showFavorites, setShowFavorites] = useState(false)
  
  // Get unique departments and research areas using helper functions
  const uniqueDepartments = getUniqueDepartments()
  const uniqueResearchAreas = getUniqueResearchAreas()
  
  // Check for recently viewed and favorites on client-side
  useEffect(() => {
    // Wait a bit to avoid hydration mismatch
    const timer = setTimeout(() => {
      if (typeof window !== 'undefined') {
        const recentData = localStorage.getItem('recently-viewed-faculty')
        const favoritesData = localStorage.getItem('faculty-favorites')
        
        setShowRecent(!!recentData && JSON.parse(recentData).length > 0)
        setShowFavorites(!!favoritesData && JSON.parse(favoritesData).length > 0)
      }
    }, 100)
    
    return () => clearTimeout(timer)
  }, [])
  
  // Generate search suggestions on component mount
  useEffect(() => {
    // Combine common search terms from faculty data
    const suggestions = [
      // Add faculty names
      ...facultyData.map(f => f.name),
      // Add departments
      ...uniqueDepartments,
      // Add common research areas
      ...uniqueResearchAreas,
      // Add some common expertise/topics
      'Artificial Intelligence', 'Computer Science', 'Business', 'Education', 
      'Cybersecurity', 'Machine Learning', 'Climate Science', 'Sustainability',
      'Research', 'Teaching', 'Publications', 'Courses'
    ];
    
    // Filter unique suggestions
    setSearchSuggestions([...new Set(suggestions)]);
  }, []);
  
  // Process faculty data for the org chart
  const departmentsForOrgChart = useMemo(() => {
    const deptMap = new Map()
    
    facultyData.forEach(faculty => {
      if (!deptMap.has(faculty.department)) {
        deptMap.set(faculty.department, {
          name: faculty.department,
          faculty: [],
        })
      }
      
      const dept = deptMap.get(faculty.department)
      dept.faculty.push(faculty)
      
      // Set department head/chair (using basic heuristic of having "Dean", "Chair", or "Director" in title)
      if (!dept.head && 
          (faculty.title.includes('Dean') || 
           faculty.title.includes('Chair') || 
           faculty.title.includes('Director'))) {
        dept.head = faculty
      }
    })
    
    return Array.from(deptMap.values())
  }, [])
  
  // Apply filters to faculty data
  const filteredFaculty = useMemo(() => {
    let filtered = facultyData;
    
    // Apply search filter
    if (filters.search) {
      filtered = searchFaculty(filters.search);
    }
    
    // Apply department filter
    if (filters.department) {
      filtered = filterFacultyByDepartment(filters.department);
    }
    
    // Apply research area filter
    if (filters.researchArea) {
      filtered = filterFacultyByResearchArea(filters.researchArea);
    }
    
    // Apply expertise filters
    if (filters.expertiseFilter.length > 0) {
      filtered = filtered.filter(faculty => {
        const facultyText = [
          faculty.bio,
          ...faculty.research,
          ...faculty.education,
        ].join(' ').toLowerCase();
        
        return filters.expertiseFilter.some(expertise => 
          facultyText.includes(expertise.toLowerCase())
        );
      });
    }
    
    return filtered;
  }, [filters])

  // Handle filter changes with a proper callback
  const handleFilterChange = useMemo(() => {
    return (newFilters: typeof filters) => {
      setFilters(newFilters);
      // Only update viewMode if it's actually different
      if (newFilters.viewMode !== viewMode) {
        setViewMode(newFilters.viewMode);
      }
    };
  }, [viewMode]);

  // Handle view mode changes with a proper callback
  const handleViewModeChange = useMemo(() => {
    return (mode: 'grid' | 'org' | 'research') => {
      setViewMode(mode);
      setFilters(prev => ({ ...prev, viewMode: mode }));
    };
  }, []);

  // Handle storage events (favorites or recently viewed changes)
  useEffect(() => {
    const handleStorageChange = () => {
      if (typeof window !== 'undefined') {
        const recentData = localStorage.getItem('recently-viewed-faculty')
        const favoritesData = localStorage.getItem('faculty-favorites')
        
        setShowRecent(!!recentData && JSON.parse(recentData).length > 0)
        setShowFavorites(!!favoritesData && JSON.parse(favoritesData).length > 0)
      }
    }
    
    window.addEventListener('storage', handleStorageChange)
    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Page Header - With Subtle Gradient */}
          <section className="bg-gradient-to-b from-light to-background py-8 relative overflow-hidden">
            {/* Subtle background decorative elements */}
            <div className="absolute inset-0 overflow-hidden opacity-10">
              <div className="absolute -top-20 -left-20 w-96 h-96 bg-crimson rounded-full blur-3xl"></div>
              <div className="absolute top-1/2 right-0 w-80 h-80 bg-gold rounded-full blur-3xl"></div>
            </div>
            
            <div className="container mx-auto px-4 max-w-6xl relative">
              {/* Breadcrumb */}
              <div className="mb-6">
                <Breadcrumb items={[{ label: 'Faculty', href: '/faculty', current: true }]} />
              </div>
              
              <div className="max-w-3xl">
                <div className="inline-flex items-center px-3 py-1 rounded-full bg-crimson/10 text-crimson text-sm font-medium mb-2 w-fit">
                  Our Faculty
                </div>
                <h1 className="text-2xl font-medium tracking-tight text-gray-900 mb-2">
                  Faculty Directory
                </h1>
                <p className="text-base text-muted-foreground">
                  Connect with our expert faculty across all academic departments.
                </p>
              </div>
            </div>
          </section>
          
          {/* Faculty Directory with filters */}
          <section className="py-8 bg-gradient-to-b from-background to-light/50">
            <div className="container mx-auto px-4 max-w-6xl">
              {/* Filters - Streamlined */}
              <div className="mb-8">
                <FacultyFilters 
                  departments={uniqueDepartments}
                  researchAreas={uniqueResearchAreas}
                  onFilterChange={handleFilterChange}
                  activeViewMode={viewMode}
                  onViewModeChange={handleViewModeChange}
                  searchSuggestions={searchSuggestions}
                />
              </div>
              
              {/* Recently Viewed - Compact and subtle */}
              {showRecent && (
                <div className="mb-8">
                  <RecentlyViewedFaculty />
                </div>
              )}
              
              {/* Favorites - Compact and subtle */}
              {showFavorites && (
                <div className="mb-8">
                  <FacultyFavorites />
                </div>
              )}
              
              {/* View Content Based on Selected Mode */}
              <div>
                {/* Grid View - Clean and minimalist cards */}
                {viewMode === 'grid' && (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-5">
                    {filteredFaculty.map((faculty) => (
                      <Link key={faculty.id} href={`/faculty/${faculty.id}`}>
                        <Card className="h-full border hover:shadow-sm transition-shadow overflow-hidden group">
                          <div className="aspect-[3/2] w-full overflow-hidden bg-gray-100">
                            <LazyImage
                              src={faculty.imageUrl}
                              alt={faculty.altText}
                              aspectRatio="aspect-[3/2]"
                              className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
                            />
                          </div>
                          <CardContent className="p-4">
                            <div className="text-xs uppercase tracking-wide text-gray-500 mb-1">
                              {faculty.department}
                            </div>
                            <h2 className="text-base font-medium text-gray-900 mb-1 line-clamp-1">
                              {faculty.name}
                            </h2>
                            <div className="text-sm text-gray-600 mb-3 line-clamp-1">
                              {faculty.title}
                            </div>
                            
                            <div className="flex items-center justify-between mt-auto">
                              <div className="flex space-x-1.5">
                                <a 
                                  href={`mailto:${faculty.email}`} 
                                  onClick={(e) => e.stopPropagation()}
                                  className="inline-flex items-center justify-center h-7 w-7 rounded-full bg-gray-100 text-gray-600 hover:bg-crimson/10 hover:text-crimson transition-colors"
                                  aria-label={`Email ${faculty.name}`}
                                >
                                  <Mail className="h-3.5 w-3.5" />
                                </a>
                                <a 
                                  href={faculty.website} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  onClick={(e) => e.stopPropagation()} 
                                  className="inline-flex items-center justify-center h-7 w-7 rounded-full bg-gray-100 text-gray-600 hover:bg-crimson/10 hover:text-crimson transition-colors"
                                  aria-label={`Visit ${faculty.name}'s website`}
                                >
                                  <Globe className="h-3.5 w-3.5" />
                                </a>
                              </div>
                              
                              <span className="text-xs text-gray-500 flex items-center group-hover:text-crimson transition-colors">
                                View Profile
                                <ArrowRight className="ml-1 h-3 w-3" />
                              </span>
                            </div>
                          </CardContent>
                        </Card>
                      </Link>
                    ))}
                  </div>
                )}
                
                {/* Org Chart View */}
                {viewMode === 'org' && (
                  <div>
                    <FacultyOrgChart 
                      departments={departmentsForOrgChart}
                      selectedDepartment={filters.department}
                    />
                  </div>
                )}
                
                {/* Research Network View */}
                {viewMode === 'research' && (
                  <div>
                    <FacultyResearchNetwork 
                      faculty={facultyData}
                      researchAreas={researchAreas}
                      selectedDepartment={filters.department}
                      selectedResearchArea={filters.researchArea}
                    />
                  </div>
                )}
                
                {/* No Results - Simplified */}
                {filteredFaculty.length === 0 && (
                  <div className="text-center py-12 border rounded-md bg-gradient-to-b from-background to-light/50">
                    <Search className="h-8 w-8 mx-auto text-gray-400 mb-3" />
                    <h3 className="text-base font-medium text-gray-900 mb-1">No Faculty Found</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      No faculty members match your current filters.
                    </p>
                    <Button 
                      variant="outline" 
                      onClick={() => {
                        setFilters({
                          search: '',
                          department: '',
                          researchArea: '',
                          expertiseFilter: [],
                          viewMode
                        })
                      }}
                      size="sm"
                      className="h-8 px-3 text-xs border-crimson/20 hover:bg-crimson/5"
                    >
                      Clear Filters
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </PageTransition>
  )
} 