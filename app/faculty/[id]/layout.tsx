import React from 'react'
import { Metadata } from 'next'
import { getFacultyById } from "@/lib/data/faculty"

type Props = {
  params: { id: string }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  // Get faculty data from centralized data
  const { id } = await params;
  const faculty = await getFacultyById(id)

  if (!faculty) {
    return {
      title: 'Faculty Not Found | College Name',
      description: 'The requested faculty profile could not be found.'
    }
  }

  return {
    title: `${faculty.name} | ${faculty.department} | College Name`,
    description: faculty.bio,
    openGraph: {
      title: `${faculty.name} - ${faculty.title}`,
      description: faculty.bio,
      images: [
        {
          url: faculty.imageUrl,
          width: 800,
          height: 600,
          alt: faculty.altText
        }
      ],
      type: 'profile',
      firstName: faculty.name.split(' ')[0],
      lastName: faculty.name.split(' ').slice(1).join(' '),
      username: faculty.id
    }
  }
}

export default function FacultyProfileLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
}