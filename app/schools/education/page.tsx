'use client'

import Link from "next/link"
import { ArrowRight, BookOpen, ChevronRight, GraduationCap, Pencil, School, Presentation, Brain, Users, Globe } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useParallaxEffect } from "@/components/parallax-effect"
import { PageTransition } from "@/components/ui/page-transition"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LazyImage } from "@/components/ui/lazy-image"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { StickyCTA } from "@/components/ui/sticky-cta"
import { SkipLink } from "@/components/ui/skip-link"

// Data for Education Programs
const educationPrograms = [
  { 
    name: "B.Ed. in Elementary Education", 
    slug: "bed-elementary", 
    icon: <BookOpen className="h-4 w-4 text-blue-500" />,
    description: "K-6 teaching fundamentals",
    details: "Prepare to teach in elementary schools with a focus on child development, literacy, mathematics, and creating inclusive learning environments.",
  },
  { 
    name: "B.Ed. in Secondary Education", 
    slug: "bed-secondary", 
    icon: <School className="h-4 w-4 text-blue-500" />,
    description: "Specialized subject teaching",
    details: "Specialize in teaching specific subjects at the secondary level with deep content knowledge and advanced pedagogical strategies.",
  },
  { 
    name: "B.Ed. in Special Education", 
    slug: "bed-special", 
    icon: <Users className="h-4 w-4 text-blue-500" />,
    description: "Inclusive education approaches",
    details: "Learn to adapt teaching methods and create supportive environments for students with diverse learning needs and abilities.",
  },
  { 
    name: "B.Ed. in Educational Technology", 
    slug: "bed-edtech", 
    icon: <Presentation className="h-4 w-4 text-blue-500" />,
    description: "Technology-enhanced learning",
    details: "Integrate digital tools and innovative technologies into teaching practices to enhance student engagement and learning outcomes.",
  },
];

const additionalPrograms = [
  {
    name: "Curriculum Design",
    slug: "bed-curriculum-design",
    description: "Educational content development",
    details: "Learn to develop effective curricula that align with educational standards while engaging students and promoting critical thinking.",
    icon: <Pencil className="h-6 w-6 text-blue-500" />,
  },
  {
    name: "Educational Psychology",
    slug: "bed-educational-psychology",
    description: "Understanding learning processes",
    details: "Study how students learn, develop, and process information to create more effective teaching approaches and learning environments.",
    icon: <Brain className="h-6 w-6 text-blue-500" />,
  },
  {
    name: "Global Education",
    slug: "bed-global-education",
    description: "International teaching perspectives",
    details: "Explore international educational systems and develop cultural competency for teaching in diverse and global contexts.",
    icon: <Globe className="h-6 w-6 text-blue-500" />,
  },
];

const facultyHighlights = [
  {
    name: "Dr. Sarah Williams",
    role: "Department Chair, Elementary Education",
    image: "/placeholder.svg?height=200&width=200",
    details: "With over 20 years of classroom experience and research in literacy development, Dr. Williams leads initiatives to transform early childhood education methods.",
  },
  {
    name: "Dr. James Chen",
    role: "Professor, Educational Technology",
    image: "/placeholder.svg?height=200&width=200",
    details: "Award-winning educator who specializes in technology integration in classrooms with numerous publications on blended learning approaches.",
  },
  {
    name: "Dr. Maria Rodriguez",
    role: "Associate Professor, Special Education",
    image: "/placeholder.svg?height=200&width=200", 
    details: "Advocate for inclusive education with extensive experience designing interventions for students with learning differences and developmental disabilities.",
  },
];

const researchAreas = [
  {
    title: "Innovative Pedagogies",
    description: "Researching and developing new teaching methodologies that enhance student engagement, critical thinking, and knowledge retention.",
    link: "/research/innovative-pedagogies",
  },
  {
    title: "Digital Learning Environments",
    description: "Exploring how technology can transform educational experiences through online platforms, simulations, and immersive learning environments.",
    link: "/research/digital-learning",
  },
  {
    title: "Educational Equity & Inclusion",
    description: "Addressing barriers to educational access and developing strategies to create more equitable and inclusive learning experiences for all students.",
    link: "/research/educational-equity",
  },
];

const facilities = [
  {
    name: "Teaching & Learning Lab",
    description: "Interactive classroom environment where students practice teaching methods and receive immediate feedback through video recording and analysis",
    icon: <Presentation className="h-6 w-6 text-blue-500" />,
  },
  {
    name: "Educational Technology Center",
    description: "State-of-the-art facility with the latest educational technologies, software, and interactive learning tools for exploring innovations in teaching",
    icon: <GraduationCap className="h-6 w-6 text-blue-500" />,
  },
  {
    name: "Child Development Center",
    description: "On-campus preschool and elementary environment providing supervised real-world teaching experience for education students",
    icon: <Users className="h-6 w-6 text-blue-500" />,
  },
];

const partnerSchools = [
  {
    name: "Local Partner Schools",
    partners: ["Ullens School", "XYZ Public School", "ABC International School"],
  },
  {
    name: "International Partnerships",
    partners: ["Cambridge Education Group", "International Baccalaureate", "Global Teacher Education"],
  },
  {
    name: "Community Organizations",
    partners: ["Children's Learning Initiative", "Inclusive Education Network", "STEM Teaching Coalition"],
  },
];

export default function EducationPage() {
  useParallaxEffect();

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Hero Section */}
          <section className="w-full py-16 md:py-24 lg:py-32 bg-gradient-to-b from-blue-50 to-background relative overflow-hidden">
            <div className="absolute inset-0 overflow-hidden z-0">
              <div className="absolute -right-20 top-20 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl" />
              <div className="absolute left-1/4 bottom-0 w-96 h-96 bg-indigo-500/5 rounded-full blur-3xl" />
            </div>
            
            <div className="px-4 md:px-6 relative">
              <div className="grid gap-10 lg:grid-cols-2 lg:gap-16 items-center">
                <div className="flex flex-col justify-center space-y-6">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-blue-500/10 text-blue-500 text-sm font-medium w-fit">
                    School of Education
                  </div>
                  <div className="space-y-4">
                    <h1 className="heading-xl">
                      Shaping the Future of <span className="text-blue-500">Education</span>
                    </h1>
                    <p className="max-w-[600px] text-muted-foreground md:text-xl leading-relaxed">
                      Our School of Education prepares educators who inspire learning and make a positive impact on students' lives, shaping the next generation.
                    </p>
                  </div>
                  <div className="flex flex-col gap-3 min-[400px]:flex-row pt-4">
                    <Button className="shadow-md transition-all hover:shadow-lg bg-blue-500 hover:bg-blue-600">
                      Explore Programs
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                    <Button variant="outline" className="border-blue-500/20 hover:bg-blue-500/5">
                      Schedule a Visit
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-center relative">
                  <div className="absolute -z-10 w-[120%] h-[120%] bg-gradient-to-tr from-blue-500/10 via-indigo-500/10 to-transparent rounded-full blur-2xl"></div>
                  <div className="relative w-full max-w-[550px] rounded-2xl overflow-hidden shadow-xl">
                    <img
                      src="/placeholder.svg?height=550&width=550"
                      width={550}
                      height={550}
                      alt="Education students collaborating"
                      className="aspect-square w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-blue-500/20 to-transparent mix-blend-multiply"></div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Department Overview */}
          <section className="w-full py-16 md:py-24 bg-light">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-blue-500/10 text-blue-500 text-sm font-medium">
                    Department Overview
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Excellence in Teacher Education
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our School of Education is committed to preparing skilled, passionate educators through a blend of theory, practice, and innovation that empowers them to meet the diverse needs of today's students.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  <div className="bg-background p-8 rounded-2xl shadow-sm flex flex-col items-center text-center">
                    <div className="h-16 w-16 rounded-full bg-blue-500/10 flex items-center justify-center mb-4">
                      <BookOpen className="h-8 w-8 text-blue-500" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Research-Based Approach</h3>
                    <p className="text-muted-foreground">
                      Our curriculum integrates the latest educational research with practical classroom applications to prepare teachers who can implement evidence-based practices.
                    </p>
                  </div>
                  
                  <div className="bg-background p-8 rounded-2xl shadow-sm flex flex-col items-center text-center">
                    <div className="h-16 w-16 rounded-full bg-blue-500/10 flex items-center justify-center mb-4">
                      <Presentation className="h-8 w-8 text-blue-500" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Hands-On Experience</h3>
                    <p className="text-muted-foreground">
                      Students gain extensive classroom experience through field placements, student teaching, and our on-campus teaching lab with real-world scenarios.
                    </p>
                  </div>

                  <div className="bg-background p-8 rounded-2xl shadow-sm flex flex-col items-center text-center">
                    <div className="h-16 w-16 rounded-full bg-blue-500/10 flex items-center justify-center mb-4">
                      <Users className="h-8 w-8 text-blue-500" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Inclusive Education</h3>
                    <p className="text-muted-foreground">
                      Our programs emphasize creating inclusive learning environments that address the needs of diverse student populations with varied learning styles.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Featured Program Section */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="grid items-center gap-10 lg:grid-cols-2 lg:gap-16 max-w-6xl mx-auto">
                <div className="flex items-center justify-center relative">
                  <div className="absolute -z-10 w-[120%] h-[120%] bg-gradient-to-tr from-blue-500/10 via-indigo-500/10 to-transparent rounded-full blur-2xl"></div>
                  <LazyImage
                    src="/placeholder.svg?height=450&width=450"
                    alt="Elementary education classroom"
                    aspectRatio="aspect-square"
                    className="rounded-2xl shadow-lg"
                    width={450}
                    height={450}
                  />
                </div>

                <div className="flex flex-col justify-center space-y-6">
                  <div className="space-y-4">
                    <div className="inline-flex items-center rounded-full px-3 py-1 text-sm font-medium bg-blue-500 text-white">
                      Featured Program
                    </div>
                    <h3 className="heading-md">B.Ed. in Elementary Education</h3>
                    <p className="text-muted-foreground text-lg leading-relaxed">
                      Our elementary education program combines foundational teaching methodology with specialized knowledge in child development, literacy, mathematics, and sciences. Students develop practical skills through extensive fieldwork and student teaching experiences.
                    </p>
                  </div>
                  <ul className="grid gap-3 mt-2">
                    <li className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500/10">
                        <BookOpen className="h-4 w-4 text-blue-500" />
                      </div>
                      <span className="font-medium">Comprehensive Teaching Foundation</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500/10">
                        <Users className="h-4 w-4 text-blue-500" />
                      </div>
                      <span className="font-medium">Child Development Focus</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500/10">
                        <School className="h-4 w-4 text-blue-500" />
                      </div>
                      <span className="font-medium">Extensive Classroom Experience</span>
                    </li>
                  </ul>
                  <div className="pt-2">
                    <Button asChild variant="outline" className="border-blue-500/20 hover:bg-blue-500/5">
                      <Link href="/programs/bed-elementary">Learn More</Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Program Cards Section */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-blue-500/10 text-blue-500 text-sm font-medium">
                    Our Programs
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Comprehensive Education Programs
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Explore our diverse range of education programs designed to prepare you for successful careers in various educational settings.
                    </p>
                  </div>
                </div>

                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 mt-12">
                  {[...educationPrograms, ...additionalPrograms].map((program, index) => (
                    <Link key={index} href={`/programs/${program.slug}`} className="block card-hover no-underline">
                      <Card className="h-full border-0 bg-light shadow-sm transition-all duration-300 hover:shadow-md">
                        <CardHeader>
                          <CardTitle className="text-xl text-foreground">{program.name}</CardTitle>
                          {program.description && <CardDescription className="text-muted-foreground/80">{program.description}</CardDescription>}
                        </CardHeader>
                        <CardContent>
                          <p className="text-muted-foreground leading-relaxed">
                            {program.details || `Explore various aspects of ${program.name}.`}
                          </p>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Research Areas */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-background to-light/50">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-blue-500/10 text-blue-500 text-sm font-medium">
                    Research
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Educational Research Initiatives
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our faculty and students engage in cutting-edge educational research that addresses contemporary challenges and contributes to teaching theory and practice.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
                  {researchAreas.map((area, index) => (
                    <div key={index} className="bg-background p-6 rounded-xl border border-border/50 hover:shadow-md transition-all duration-300">
                      <h3 className="text-xl font-bold mb-3">{area.title}</h3>
                      <p className="text-muted-foreground mb-4">
                        {area.description}
                      </p>
                      <Link href={area.link} className="inline-flex items-center text-blue-500 hover:underline">
                        Learn more <ChevronRight className="h-4 w-4 ml-1" />
                      </Link>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Faculty Highlights */}
          <section className="w-full py-16 md:py-24 bg-light">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-blue-500/10 text-blue-500 text-sm font-medium">
                    Our Faculty
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Learn from Education Experts
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our distinguished faculty members bring a wealth of classroom experience and research expertise, providing students with real-world insights.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  {facultyHighlights.map((faculty, index) => (
                    <div key={index} className="bg-background rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
                      <div className="h-48 overflow-hidden">
                        <img 
                          src={faculty.image} 
                          alt={faculty.name} 
                          className="w-full h-full object-cover object-center"
                        />
                      </div>
                      <div className="p-6">
                        <h3 className="text-xl font-bold mb-1">{faculty.name}</h3>
                        <p className="text-blue-500 text-sm font-medium mb-3">{faculty.role}</p>
                        <p className="text-muted-foreground">{faculty.details}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="text-center mt-12">
                  <Button asChild variant="outline" className="border-blue-500/20 hover:bg-blue-500/5">
                    <Link href="/faculty">View All Faculty</Link>
                  </Button>
                </div>
              </div>
            </div>
          </section>

          {/* Facilities Section */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-blue-500/10 text-blue-500 text-sm font-medium">
                    Our Facilities
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      State-of-the-Art Teaching Labs
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our specialized facilities provide students with the tools and experience to develop practical teaching skills in realistic classroom settings.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  {facilities.map((facility, index) => (
                    <div key={index} className="bg-light p-8 rounded-xl flex flex-col items-center text-center">
                      <div className="h-16 w-16 rounded-full bg-blue-500/10 flex items-center justify-center mb-4">
                        {facility.icon}
                      </div>
                      <h3 className="text-xl font-bold mb-3">{facility.name}</h3>
                      <p className="text-muted-foreground">{facility.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Partner Schools */}
          <section className="w-full py-16 md:py-24 bg-light/50">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-blue-500/10 text-blue-500 text-sm font-medium">
                    Education Partnerships
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Valuable Teaching Placements
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our partnerships with local and international schools provide students with diverse teaching experiences and placement opportunities.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  {partnerSchools.map((category, index) => (
                    <div key={index} className="bg-background p-6 rounded-xl border border-border/50">
                      <h3 className="text-xl font-bold mb-4 text-blue-500">{category.name}</h3>
                      <ul className="space-y-2">
                        {category.partners.map((partner, partnerIndex) => (
                          <li key={partnerIndex} className="flex items-center gap-2">
                            <div className="h-2 w-2 rounded-full bg-blue-500/70"></div>
                            <span className="text-muted-foreground">{partner}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-background to-blue-50">
            <div className="px-4 md:px-6">
              <div className="max-w-5xl mx-auto bg-background rounded-2xl shadow-lg overflow-hidden">
                <div className="grid grid-cols-1 lg:grid-cols-2">
                  <div className="p-8 md:p-12 flex flex-col justify-center">
                    <h2 className="text-3xl font-bold mb-4">Begin Your Teaching Journey</h2>
                    <p className="text-muted-foreground mb-6">
                      Join our School of Education and gain the knowledge, skills, and experience needed to inspire the next generation of learners.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <Button className="shadow-md bg-blue-500 hover:bg-blue-600">
                        Apply Now
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                      <Button variant="outline" className="border-blue-500/20 hover:bg-blue-500/5">
                        Request Information
                      </Button>
                    </div>
                  </div>
                  <div className="hidden lg:block relative">
                    <img
                      src="/placeholder.svg?height=400&width=500"
                      alt="Education students teaching"
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-transparent mix-blend-multiply"></div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
        <StickyCTA />
      </div>
    </PageTransition>
  );
} 