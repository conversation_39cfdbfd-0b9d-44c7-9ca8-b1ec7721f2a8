'use client'

import Link from "next/link"
import { ArrowRight, Leaf, ChevronRight, Cloud, SunMedium, Sprout, Droplets, Wind, Trees } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useParallaxEffect } from "@/components/parallax-effect"
import { PageTransition } from "@/components/ui/page-transition"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LazyImage } from "@/components/ui/lazy-image"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { StickyCTA } from "@/components/ui/sticky-cta"
import { SkipLink } from "@/components/ui/skip-link"

// Data for Agriculture Programs
const agriculturalPrograms = [
  { 
    name: "B.Sc. in Sustainable Agriculture", 
    slug: "bsc-sustainable-agriculture", 
    icon: <Leaf className="h-4 w-4 text-green-600" />,
    description: "Sustainable farming methods",
    details: "Learn innovative and environmentally friendly approaches to agricultural production that maintain ecosystem health while ensuring food security and economic viability.",
  },
  { 
    name: "B.Sc. in Climate Science", 
    slug: "bsc-climate-science", 
    icon: <Cloud className="h-4 w-4 text-green-600" />,
    description: "Understanding climate systems",
    details: "Study the Earth's climate system, climate change impacts, and develop strategies for climate adaptation and mitigation for a sustainable future.",
  },
  { 
    name: "B.Sc. in Environmental Management", 
    slug: "bsc-environmental-management", 
    icon: <Trees className="h-4 w-4 text-green-600" />,
    description: "Conservation and resource management",
    details: "Focus on managing natural resources sustainably while balancing economic development with environmental conservation and social responsibility.",
  },
];

const additionalPrograms = [
  {
    name: "Agroecology",
    slug: "bsc-agroecology",
    description: "Ecological approaches to agriculture",
    details: "Study the ecology of agricultural systems and design farming practices that work with natural processes to create sustainable food production systems.",
    icon: <Sprout className="h-6 w-6 text-green-600" />,
  },
  {
    name: "Water Resource Management",
    slug: "bsc-water-resources",
    description: "Sustainable water systems",
    details: "Learn to manage water resources sustainably for agriculture, ecosystems, and communities in the face of increasing scarcity and climate change.",
    icon: <Droplets className="h-6 w-6 text-green-600" />,
  },
  {
    name: "Renewable Energy in Agriculture",
    slug: "bsc-renewable-energy-agriculture",
    description: "Clean energy for farming",
    details: "Explore the integration of renewable energy systems in agricultural operations to reduce carbon footprints and increase sustainability.",
    icon: <Wind className="h-6 w-6 text-green-600" />,
  },
];

const facultyHighlights = [
  {
    name: "Dr. Aarav Sharma",
    role: "Department Chair, Sustainable Agriculture",
    image: "/placeholder.svg?height=200&width=200",
    details: "With over 20 years of experience in agricultural innovation, Dr. Sharma leads research on developing climate-resilient crop varieties and sustainable farming systems.",
  },
  {
    name: "Dr. Nisha Patel",
    role: "Professor, Climate Science",
    image: "/placeholder.svg?height=200&width=200",
    details: "A renowned climate scientist with contributions to the IPCC, Dr. Patel specializes in climate modeling and developing adaptation strategies for agricultural systems.",
  },
  {
    name: "Dr. Rohan Singh",
    role: "Associate Professor, Environmental Management",
    image: "/placeholder.svg?height=200&width=200", 
    details: "Environmental policy expert with experience working with government agencies and NGOs on conservation projects and sustainable resource management.",
  },
];

const researchAreas = [
  {
    title: "Climate-Resilient Agriculture",
    description: "Developing farming systems that can withstand changing climate conditions while maintaining productivity and reducing environmental impacts.",
    link: "/research/climate-resilient-agriculture",
  },
  {
    title: "Agroforestry Systems",
    description: "Researching the integration of trees and shrubs into crop and animal farming systems for environmental, economic, and social benefits.",
    link: "/research/agroforestry",
  },
  {
    title: "Climate Change Monitoring",
    description: "Utilizing advanced technologies to monitor climate patterns and their effects on agricultural systems and natural ecosystems.",
    link: "/research/climate-monitoring",
  },
];

const facilities = [
  {
    name: "Sustainable Farming Demonstration Site",
    description: "5-acre demonstration farm showcasing sustainable agricultural practices, organic farming methods, and agroecology systems",
    icon: <Leaf className="h-6 w-6 text-green-600" />,
  },
  {
    name: "Climate Monitoring Station",
    description: "State-of-the-art facility for monitoring weather patterns, collecting climate data, and analyzing climate trends",
    icon: <Cloud className="h-6 w-6 text-green-600" />,
  },
  {
    name: "Environmental Analysis Laboratory",
    description: "Fully equipped lab for soil, water, and plant tissue analysis to support research and teaching in environmental management",
    icon: <SunMedium className="h-6 w-6 text-green-600" />,
  },
];

export default function AgricultureClimatePage() {
  useParallaxEffect();

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Hero Section */}
          <section className="w-full py-16 md:py-24 lg:py-32 bg-gradient-to-b from-green-50 to-background relative overflow-hidden">
            <div className="absolute inset-0 overflow-hidden z-0">
              <div className="absolute -right-20 top-20 w-80 h-80 bg-green-600/5 rounded-full blur-3xl" />
              <div className="absolute left-1/4 bottom-0 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl" />
            </div>
            
            <div className="px-4 md:px-6 relative">
              <div className="grid gap-10 lg:grid-cols-2 lg:gap-16 items-center">
                <div className="flex flex-col justify-center space-y-6">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-green-600/10 text-green-600 text-sm font-medium w-fit">
                    School of Agriculture and Climate Science
                  </div>
                  <div className="space-y-4">
                    <h1 className="heading-xl">
                      Sustainable Solutions for a <span className="text-green-600">Changing World</span>
                    </h1>
                    <p className="max-w-[600px] text-muted-foreground md:text-xl leading-relaxed">
                      Our School of Agriculture and Climate Science is dedicated to developing sustainable agricultural practices and addressing climate challenges.
                    </p>
                  </div>
                  <div className="flex flex-col gap-3 min-[400px]:flex-row pt-4">
                    <Button className="shadow-md transition-all hover:shadow-lg bg-green-600 hover:bg-green-700">
                      Explore Programs
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                    <Button variant="outline" className="border-green-600/20 hover:bg-green-600/5">
                      Schedule a Visit
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-center relative">
                  <div className="absolute -z-10 w-[120%] h-[120%] bg-gradient-to-tr from-green-600/10 via-blue-500/10 to-transparent rounded-full blur-2xl"></div>
                  <div className="relative w-full max-w-[550px] rounded-2xl overflow-hidden shadow-xl">
                    <img
                      src="/placeholder.svg?height=550&width=550"
                      width={550}
                      height={550}
                      alt="Sustainable agriculture field"
                      className="aspect-square w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-green-600/20 to-transparent mix-blend-multiply"></div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Department Overview */}
          <section className="w-full py-16 md:py-24 bg-light">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-green-600/10 text-green-600 text-sm font-medium">
                    Department Overview
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Leading the Way in Agricultural Innovation
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our School of Agriculture and Climate Science prepares students to address the complex challenges of food security, environmental sustainability, and climate change through innovative research and practical solutions.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  <div className="bg-background p-8 rounded-2xl shadow-sm flex flex-col items-center text-center">
                    <div className="h-16 w-16 rounded-full bg-green-600/10 flex items-center justify-center mb-4">
                      <Leaf className="h-8 w-8 text-green-600" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Sustainable Agriculture</h3>
                    <p className="text-muted-foreground">
                      We focus on practices that preserve ecosystems while ensuring food security and supporting farmer livelihoods.
                    </p>
                  </div>
                  
                  <div className="bg-background p-8 rounded-2xl shadow-sm flex flex-col items-center text-center">
                    <div className="h-16 w-16 rounded-full bg-green-600/10 flex items-center justify-center mb-4">
                      <Cloud className="h-8 w-8 text-green-600" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Climate Science</h3>
                    <p className="text-muted-foreground">
                      Understanding climate patterns and developing adaptation strategies for agricultural systems and ecosystems.
                    </p>
                  </div>

                  <div className="bg-background p-8 rounded-2xl shadow-sm flex flex-col items-center text-center">
                    <div className="h-16 w-16 rounded-full bg-green-600/10 flex items-center justify-center mb-4">
                      <Trees className="h-8 w-8 text-green-600" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Environmental Management</h3>
                    <p className="text-muted-foreground">
                      Balancing conservation with sustainable use of natural resources to protect biodiversity and ecosystem services.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Featured Program Section */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="grid items-center gap-10 lg:grid-cols-2 lg:gap-16 max-w-6xl mx-auto">
                <div className="flex items-center justify-center relative">
                  <div className="absolute -z-10 w-[120%] h-[120%] bg-gradient-to-tr from-green-600/10 via-amber-300/10 to-transparent rounded-full blur-2xl"></div>
                  <LazyImage
                    src="/placeholder.svg?height=450&width=450"
                    alt="Sustainable agriculture practices"
                    aspectRatio="aspect-square"
                    className="rounded-2xl shadow-lg"
                    width={450}
                    height={450}
                  />
                </div>

                <div className="flex flex-col justify-center space-y-6">
                  <div className="space-y-4">
                    <div className="inline-flex items-center rounded-full px-3 py-1 text-sm font-medium bg-green-600 text-white">
                      Featured Program
                    </div>
                    <h3 className="heading-md">B.Sc. in Sustainable Agriculture</h3>
                    <p className="text-muted-foreground text-lg leading-relaxed">
                      Our Sustainable Agriculture program teaches students how to grow food and manage agricultural systems in ways that protect the environment, support communities, and ensure long-term viability. Learn about organic farming, agroecology, and regenerative agriculture practices.
                    </p>
                  </div>
                  <ul className="grid gap-3 mt-2">
                    <li className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-600/10">
                        <Leaf className="h-4 w-4 text-green-600" />
                      </div>
                      <span className="font-medium">Regenerative Farming Techniques</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-600/10">
                        <Sprout className="h-4 w-4 text-green-600" />
                      </div>
                      <span className="font-medium">Agroecological Systems Design</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-600/10">
                        <Droplets className="h-4 w-4 text-green-600" />
                      </div>
                      <span className="font-medium">Water Conservation Strategies</span>
                    </li>
                  </ul>
                  <div className="pt-2">
                    <Button asChild variant="outline" className="border-green-600/20 hover:bg-green-600/5">
                      <Link href="/programs/bsc-sustainable-agriculture">Learn More</Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Program Cards Section */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-green-600/10 text-green-600 text-sm font-medium">
                    Our Programs
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Preparing Leaders for a Sustainable Future
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Explore our diverse range of programs designed to prepare you for careers in sustainable agriculture, climate science, and environmental management.
                    </p>
                  </div>
                </div>

                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 mt-12">
                  {[...agriculturalPrograms, ...additionalPrograms].map((program, index) => (
                    <Link key={index} href={`/programs/${program.slug}`} className="block card-hover no-underline">
                      <Card className="h-full border-0 bg-light shadow-sm transition-all duration-300 hover:shadow-md">
                        <CardHeader>
                          <CardTitle className="text-xl text-foreground">{program.name}</CardTitle>
                          {program.description && <CardDescription className="text-muted-foreground/80">{program.description}</CardDescription>}
                        </CardHeader>
                        <CardContent>
                          <p className="text-muted-foreground leading-relaxed">
                            {program.details || `Explore various aspects of ${program.name}.`}
                          </p>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Research Areas */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-background to-light/50">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-green-600/10 text-green-600 text-sm font-medium">
                    Research
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Innovative Research for Real-World Solutions
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our faculty and students conduct cutting-edge research addressing the critical challenges of food security, climate change, and environmental sustainability.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
                  {researchAreas.map((area, index) => (
                    <div key={index} className="bg-background p-6 rounded-xl border border-border/50 hover:shadow-md transition-all duration-300">
                      <h3 className="text-xl font-bold mb-3">{area.title}</h3>
                      <p className="text-muted-foreground mb-4">
                        {area.description}
                      </p>
                      <Link href={area.link} className="inline-flex items-center text-green-600 hover:underline">
                        Learn more <ChevronRight className="h-4 w-4 ml-1" />
                      </Link>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Faculty Highlights */}
          <section className="w-full py-16 md:py-24 bg-light">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-green-600/10 text-green-600 text-sm font-medium">
                    Our Faculty
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Learn from Leading Experts
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our faculty brings extensive field experience, research expertise, and industry connections to provide students with real-world knowledge and skills.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  {facultyHighlights.map((faculty, index) => (
                    <div key={index} className="bg-background rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
                      <div className="h-48 overflow-hidden">
                        <img 
                          src={faculty.image} 
                          alt={faculty.name} 
                          className="w-full h-full object-cover object-center"
                        />
                      </div>
                      <div className="p-6">
                        <h3 className="text-xl font-bold mb-1">{faculty.name}</h3>
                        <p className="text-green-600 text-sm font-medium mb-3">{faculty.role}</p>
                        <p className="text-muted-foreground">{faculty.details}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="text-center mt-12">
                  <Button asChild variant="outline" className="border-green-600/20 hover:bg-green-600/5">
                    <Link href="/faculty">View All Faculty</Link>
                  </Button>
                </div>
              </div>
            </div>
          </section>

          {/* Facilities Section */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-green-600/10 text-green-600 text-sm font-medium">
                    Our Facilities
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Hands-On Learning Environments
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our state-of-the-art facilities provide students with practical experience in sustainable farming, climate monitoring, and environmental analysis.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  {facilities.map((facility, index) => (
                    <div key={index} className="bg-light p-8 rounded-xl flex flex-col items-center text-center">
                      <div className="h-16 w-16 rounded-full bg-green-600/10 flex items-center justify-center mb-4">
                        {facility.icon}
                      </div>
                      <h3 className="text-xl font-bold mb-3">{facility.name}</h3>
                      <p className="text-muted-foreground">{facility.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-background to-green-50">
            <div className="px-4 md:px-6">
              <div className="max-w-5xl mx-auto bg-background rounded-2xl shadow-lg overflow-hidden">
                <div className="grid grid-cols-1 lg:grid-cols-2">
                  <div className="p-8 md:p-12 flex flex-col justify-center">
                    <h2 className="text-3xl font-bold mb-4">Join Us in Creating a Sustainable Future</h2>
                    <p className="text-muted-foreground mb-6">
                      Be part of the solution to global challenges. Our programs prepare you for rewarding careers in sustainable agriculture, climate science, and environmental management.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <Button className="shadow-md bg-green-600 hover:bg-green-700">
                        Apply Now
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                      <Button variant="outline" className="border-green-600/20 hover:bg-green-600/5">
                        Request Information
                      </Button>
                    </div>
                  </div>
                  <div className="hidden lg:block relative">
                    <img
                      src="/placeholder.svg?height=400&width=500"
                      alt="Students in agriculture field"
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-green-600/20 to-transparent mix-blend-multiply"></div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
        <StickyCTA />
      </div>
    </PageTransition>
  );
} 