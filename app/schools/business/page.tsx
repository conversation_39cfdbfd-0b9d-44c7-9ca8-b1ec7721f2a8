'use client'

import Link from "next/link"
import { ArrowRight, Building, ChevronRight, DollarSign, BarChart, TrendingUp, Briefcase, PieChart, Target, Users, Globe } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useParallaxEffect } from "@/components/parallax-effect"
import { PageTransition } from "@/components/ui/page-transition"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LazyImage } from "@/components/ui/lazy-image"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { StickyCTA } from "@/components/ui/sticky-cta"
import { SkipLink } from "@/components/ui/skip-link"

// Data for Business Programs
const businessPrograms = [
  { 
    name: "Bachelor of Business Administration (BBA)", 
    slug: "bba-general", 
    icon: <Building className="h-4 w-4 text-gold" />,
    description: "Comprehensive business foundation",
    details: "Our BBA program provides a comprehensive foundation in all aspects of business, including management, marketing, finance, operations, and entrepreneurship.",
  },
  { 
    name: "B.Sc. in Finance", 
    slug: "bsc-finance", 
    icon: <DollarSign className="h-4 w-4 text-gold" />,
    description: "Financial analysis and management",
    details: "Develop expertise in financial analysis, investment management, corporate finance, and financial planning for careers in banking, investment, and corporate finance.",
  },
  { 
    name: "B.Sc. in Marketing", 
    slug: "bsc-marketing", 
    icon: <Target className="h-4 w-4 text-gold" />,
    description: "Strategic marketing and consumer behavior",
    details: "Learn how to analyze consumer behavior, develop marketing strategies, and create compelling campaigns in our specialized marketing program.",
  },
  { 
    name: "B.Sc. in International Business", 
    slug: "bsc-international-business", 
    icon: <Globe className="h-4 w-4 text-gold" />,
    description: "Global business operations",
    details: "Prepare for a career in the global marketplace by studying international trade, cross-cultural management, and global business strategies.",
  },
];

const additionalPrograms = [
  {
    name: "Entrepreneurship",
    slug: "bsc-entrepreneurship",
    description: "Building successful ventures",
    details: "Develop the skills, knowledge, and mindset to launch and grow successful business ventures in our entrepreneurship specialization.",
    icon: <TrendingUp className="h-6 w-6 text-gold" />,
  },
  {
    name: "Business Analytics",
    slug: "bsc-business-analytics",
    description: "Data-driven business decisions",
    details: "Learn to use data analysis techniques to drive strategic business decisions and gain insights from complex business data sets.",
    icon: <BarChart className="h-6 w-6 text-gold" />,
  },
  {
    name: "Human Resource Management",
    slug: "bsc-human-resources",
    description: "Strategic talent management",
    details: "Specialize in managing and developing human capital, with focus on recruitment, training, performance management, and organizational development.",
    icon: <Users className="h-6 w-6 text-gold" />,
  },
];

const facultyHighlights = [
  {
    name: "Dr. Rajiv Mehta",
    role: "Department Chair, Business Administration",
    image: "/placeholder.svg?height=200&width=200",
    details: "With extensive experience as a business consultant for Fortune 500 companies, Dr. Mehta brings real-world business expertise to our curriculum development and research initiatives.",
  },
  {
    name: "Dr. Sarah Johnson",
    role: "Professor, Finance",
    image: "/placeholder.svg?height=200&width=200",
    details: "Former investment banker with 15 years of experience in the financial sector, Dr. Johnson specializes in corporate finance, investment analysis, and financial markets.",
  },
  {
    name: "Dr. Michael Chen",
    role: "Associate Professor, Marketing",
    image: "/placeholder.svg?height=200&width=200", 
    details: "Award-winning marketing researcher who has published extensively on consumer behavior, digital marketing strategies, and brand management in top academic journals.",
  },
];

const researchAreas = [
  {
    title: "Sustainable Business Practices",
    description: "Research on environmentally and socially responsible business models, ESG investing, and corporate sustainability strategies.",
    link: "/research/sustainable-business",
  },
  {
    title: "Digital Transformation",
    description: "Exploring how businesses can effectively leverage emerging technologies to create value, innovate processes, and gain competitive advantage.",
    link: "/research/digital-transformation",
  },
  {
    title: "Entrepreneurship and Innovation",
    description: "Research on startup ecosystems, venture funding, business model innovation, and factors that drive entrepreneurial success.",
    link: "/research/entrepreneurship",
  },
];

const facilities = [
  {
    name: "Business Simulation Lab",
    description: "State-of-the-art facility where students can practice business decision-making in simulated real-world scenarios",
    icon: <Briefcase className="h-6 w-6 text-gold" />,
  },
  {
    name: "Financial Trading Room",
    description: "Equipped with real-time financial data terminals and software for investment analysis and portfolio management",
    icon: <DollarSign className="h-6 w-6 text-gold" />,
  },
  {
    name: "Innovation and Entrepreneurship Center",
    description: "Collaborative space for developing business ideas, prototyping products, and connecting with mentors and investors",
    icon: <TrendingUp className="h-6 w-6 text-gold" />,
  },
];

const industryConnections = [
  {
    name: "Investment Banking",
    partners: ["Goldman Sachs", "JP Morgan", "Morgan Stanley"],
  },
  {
    name: "Consulting",
    partners: ["McKinsey", "BCG", "Deloitte"],
  },
  {
    name: "Technology",
    partners: ["Google", "Amazon", "Microsoft"],
  },
];

export default function BusinessPage() {
  useParallaxEffect();

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Hero Section */}
          <section className="w-full py-16 md:py-24 lg:py-32 bg-gradient-to-b from-amber-50 to-background relative overflow-hidden">
            <div className="absolute inset-0 overflow-hidden z-0">
              <div className="absolute -right-20 top-20 w-80 h-80 bg-gold/5 rounded-full blur-3xl" />
              <div className="absolute left-1/4 bottom-0 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl" />
            </div>
            
            <div className="px-4 md:px-6 relative">
              <div className="grid gap-10 lg:grid-cols-2 lg:gap-16 items-center">
                <div className="flex flex-col justify-center space-y-6">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-gold/10 text-gold text-sm font-medium w-fit">
                    School of Business
                  </div>
                  <div className="space-y-4">
                    <h1 className="heading-xl">
                      Developing Tomorrow's <span className="text-gold">Business Leaders</span>
                    </h1>
                    <p className="max-w-[600px] text-muted-foreground md:text-xl leading-relaxed">
                      Our School of Business provides students with the knowledge, skills, and experiences needed to succeed in the global business environment.
                    </p>
                  </div>
                  <div className="flex flex-col gap-3 min-[400px]:flex-row pt-4">
                    <Button className="shadow-md transition-all hover:shadow-lg bg-gold hover:bg-gold/90">
                      Explore Programs
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                    <Button variant="outline" className="border-gold/20 hover:bg-gold/5">
                      Schedule a Visit
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-center relative">
                  <div className="absolute -z-10 w-[120%] h-[120%] bg-gradient-to-tr from-gold/10 via-blue-500/10 to-transparent rounded-full blur-2xl"></div>
                  <div className="relative w-full max-w-[550px] rounded-2xl overflow-hidden shadow-xl">
                    <img
                      src="/placeholder.svg?height=550&width=550"
                      width={550}
                      height={550}
                      alt="Business students collaborating"
                      className="aspect-square w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-gold/20 to-transparent mix-blend-multiply"></div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Department Overview */}
          <section className="w-full py-16 md:py-24 bg-light">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-gold/10 text-gold text-sm font-medium">
                    Department Overview
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Excellence in Business Education
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our School of Business is committed to providing a comprehensive education that balances theoretical knowledge with practical applications, preparing students for leadership roles in various business domains.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  <div className="bg-background p-8 rounded-2xl shadow-sm flex flex-col items-center text-center">
                    <div className="h-16 w-16 rounded-full bg-gold/10 flex items-center justify-center mb-4">
                      <Briefcase className="h-8 w-8 text-gold" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Industry-Relevant Curriculum</h3>
                    <p className="text-muted-foreground">
                      Our programs are designed in consultation with industry leaders to ensure students gain the skills and knowledge most valued in today's business world.
                    </p>
                  </div>
                  
                  <div className="bg-background p-8 rounded-2xl shadow-sm flex flex-col items-center text-center">
                    <div className="h-16 w-16 rounded-full bg-gold/10 flex items-center justify-center mb-4">
                      <Globe className="h-8 w-8 text-gold" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Global Business Perspective</h3>
                    <p className="text-muted-foreground">
                      Students gain an international outlook through our global business curriculum, exchange programs, and diverse faculty with international experience.
                    </p>
                  </div>

                  <div className="bg-background p-8 rounded-2xl shadow-sm flex flex-col items-center text-center">
                    <div className="h-16 w-16 rounded-full bg-gold/10 flex items-center justify-center mb-4">
                      <PieChart className="h-8 w-8 text-gold" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Experiential Learning</h3>
                    <p className="text-muted-foreground">
                      Through internships, case competitions, business simulations, and consulting projects, students apply classroom learning to real-world business situations.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Featured Program Section */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="grid items-center gap-10 lg:grid-cols-2 lg:gap-16 max-w-6xl mx-auto">
                <div className="flex items-center justify-center relative">
                  <div className="absolute -z-10 w-[120%] h-[120%] bg-gradient-to-tr from-gold/10 via-amber-300/10 to-transparent rounded-full blur-2xl"></div>
                  <LazyImage
                    src="/placeholder.svg?height=450&width=450"
                    alt="BBA students in class"
                    aspectRatio="aspect-square"
                    className="rounded-2xl shadow-lg"
                    width={450}
                    height={450}
                  />
                </div>

                <div className="flex flex-col justify-center space-y-6">
                  <div className="space-y-4">
                    <div className="inline-flex items-center rounded-full px-3 py-1 text-sm font-medium bg-gold text-white">
                      Featured Program
                    </div>
                    <h3 className="heading-md">Bachelor of Business Administration (BBA)</h3>
                    <p className="text-muted-foreground text-lg leading-relaxed">
                      Our flagship BBA program provides students with a strong foundation in all aspects of business. The curriculum integrates core business principles with specialized knowledge in students' areas of interest, complemented by hands-on experiences through internships and case competitions.
                    </p>
                  </div>
                  <ul className="grid gap-3 mt-2">
                    <li className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gold/10">
                        <Building className="h-4 w-4 text-gold" />
                      </div>
                      <span className="font-medium">Comprehensive Business Foundation</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gold/10">
                        <Target className="h-4 w-4 text-gold" />
                      </div>
                      <span className="font-medium">Specialization Opportunities</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gold/10">
                        <Briefcase className="h-4 w-4 text-gold" />
                      </div>
                      <span className="font-medium">Industry Internship Program</span>
                    </li>
                  </ul>
                  <div className="pt-2">
                    <Button asChild variant="outline" className="border-gold/20 hover:bg-gold/5">
                      <Link href="/programs/bba-general">Learn More</Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Program Cards Section */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-gold/10 text-gold text-sm font-medium">
                    Our Programs
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Comprehensive Business Education
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Explore our diverse range of business programs designed to prepare you for successful careers in various business domains.
                    </p>
                  </div>
                </div>

                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 mt-12">
                  {[...businessPrograms, ...additionalPrograms].map((program, index) => (
                    <Link key={index} href={`/programs/${program.slug}`} className="block card-hover no-underline">
                      <Card className="h-full border-0 bg-light shadow-sm transition-all duration-300 hover:shadow-md">
                        <CardHeader>
                          <CardTitle className="text-xl text-foreground">{program.name}</CardTitle>
                          {program.description && <CardDescription className="text-muted-foreground/80">{program.description}</CardDescription>}
                        </CardHeader>
                        <CardContent>
                          <p className="text-muted-foreground leading-relaxed">
                            {program.details || `Explore various aspects of ${program.name}.`}
                          </p>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Research Areas */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-background to-light/50">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-gold/10 text-gold text-sm font-medium">
                    Research
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Business Research Initiatives
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our faculty and students engage in cutting-edge research that addresses contemporary business challenges and contributes to business theory and practice.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
                  {researchAreas.map((area, index) => (
                    <div key={index} className="bg-background p-6 rounded-xl border border-border/50 hover:shadow-md transition-all duration-300">
                      <h3 className="text-xl font-bold mb-3">{area.title}</h3>
                      <p className="text-muted-foreground mb-4">
                        {area.description}
                      </p>
                      <Link href={area.link} className="inline-flex items-center text-gold hover:underline">
                        Learn more <ChevronRight className="h-4 w-4 ml-1" />
                      </Link>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Faculty Highlights */}
          <section className="w-full py-16 md:py-24 bg-light">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-gold/10 text-gold text-sm font-medium">
                    Our Faculty
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Learn from Industry Veterans
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our distinguished faculty members bring a wealth of academic expertise and industry experience to the classroom, providing students with real-world insights.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  {facultyHighlights.map((faculty, index) => (
                    <div key={index} className="bg-background rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
                      <div className="h-48 overflow-hidden">
                        <img 
                          src={faculty.image} 
                          alt={faculty.name} 
                          className="w-full h-full object-cover object-center"
                        />
                      </div>
                      <div className="p-6">
                        <h3 className="text-xl font-bold mb-1">{faculty.name}</h3>
                        <p className="text-gold text-sm font-medium mb-3">{faculty.role}</p>
                        <p className="text-muted-foreground">{faculty.details}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="text-center mt-12">
                  <Button asChild variant="outline" className="border-gold/20 hover:bg-gold/5">
                    <Link href="/faculty">View All Faculty</Link>
                  </Button>
                </div>
              </div>
            </div>
          </section>

          {/* Facilities Section */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-gold/10 text-gold text-sm font-medium">
                    Our Facilities
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      State-of-the-Art Learning Environments
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our specialized facilities provide students with the tools and resources to develop practical skills and apply business concepts in realistic settings.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  {facilities.map((facility, index) => (
                    <div key={index} className="bg-light p-8 rounded-xl flex flex-col items-center text-center">
                      <div className="h-16 w-16 rounded-full bg-gold/10 flex items-center justify-center mb-4">
                        {facility.icon}
                      </div>
                      <h3 className="text-xl font-bold mb-3">{facility.name}</h3>
                      <p className="text-muted-foreground">{facility.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Industry Connections */}
          <section className="w-full py-16 md:py-24 bg-light/50">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-gold/10 text-gold text-sm font-medium">
                    Industry Connections
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Strong Links with Top Companies
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our partnerships with leading organizations provide students with internship opportunities, guest lectures, and recruitment connections.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  {industryConnections.map((sector, index) => (
                    <div key={index} className="bg-background p-6 rounded-xl border border-border/50">
                      <h3 className="text-xl font-bold mb-4 text-gold">{sector.name}</h3>
                      <ul className="space-y-2">
                        {sector.partners.map((partner, partnerIndex) => (
                          <li key={partnerIndex} className="flex items-center gap-2">
                            <div className="h-2 w-2 rounded-full bg-gold/70"></div>
                            <span className="text-muted-foreground">{partner}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-background to-amber-50">
            <div className="px-4 md:px-6">
              <div className="max-w-5xl mx-auto bg-background rounded-2xl shadow-lg overflow-hidden">
                <div className="grid grid-cols-1 lg:grid-cols-2">
                  <div className="p-8 md:p-12 flex flex-col justify-center">
                    <h2 className="text-3xl font-bold mb-4">Start Your Business Journey Today</h2>
                    <p className="text-muted-foreground mb-6">
                      Join our School of Business and gain the knowledge, skills, and connections needed for a successful career in the global business world.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <Button className="shadow-md bg-gold hover:bg-gold/90">
                        Apply Now
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                      <Button variant="outline" className="border-gold/20 hover:bg-gold/5">
                        Request Information
                      </Button>
                    </div>
                  </div>
                  <div className="hidden lg:block relative">
                    <img
                      src="/placeholder.svg?height=400&width=500"
                      alt="Business students in class"
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-gold/20 to-transparent mix-blend-multiply"></div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
        <StickyCTA />
      </div>
    </PageTransition>
  );
} 