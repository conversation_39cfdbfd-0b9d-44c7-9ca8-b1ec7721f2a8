'use client'

import Link from "next/link"
import { ArrowRight, Lock, Code, ChevronRight, Database, Server, Network, Cpu } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useParallaxEffect } from "@/components/parallax-effect"
import { PageTransition } from "@/components/ui/page-transition"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LazyImage } from "@/components/ui/lazy-image"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { StickyCTA } from "@/components/ui/sticky-cta"
import { SkipLink } from "@/components/ui/skip-link"

// Data for Computer Science Programs
const csFeaturedProgram = {
  name: "B.Tech in Cybersecurity",
  slug: "btech-cybersecurity",
  title: "B.Tech in Cybersecurity",
  details: "Our comprehensive Cybersecurity program equips students with the skills to protect digital assets and infrastructure from evolving threats. Learn from industry experts and gain hands-on experience in our state-of-the-art security labs.",
  points: [
    { text: "Network Security & Ethical Hacking", icon: <Lock className="h-4 w-4 text-crimson" /> },
    { text: "Digital Forensics & Incident Response", icon: <Lock className="h-4 w-4 text-crimson" /> },
    { text: "Security Operations & Threat Intelligence", icon: <Lock className="h-4 w-4 text-crimson" /> },
  ],
};

const csProgramCards = [
  {
    name: "Computer Science",
    slug: "btech-cs",
    description: "Core fundamentals and advanced topics",
    details: "Our Computer Science program covers algorithms, data structures, software engineering, and more. Students develop strong problem-solving skills applicable across industries.",
  },
  {
    name: "Artificial Intelligence",
    slug: "btech-ai-ml",
    description: "Machine learning and AI applications",
    details: "Study machine learning, neural networks, and AI applications. Work on real-world projects and prepare for careers in this rapidly growing field.",
  },
  {
    name: "Data Science",
    slug: "btech-data-science",
    description: "Analytics and data-driven decision making",
    details: "Learn to extract insights from complex datasets. Develop skills in statistical analysis, data visualization, and predictive modeling.",
  },
];

const additionalPrograms = [
  {
    name: "Software Engineering",
    slug: "btech-software-engineering",
    description: "Building robust software systems",
    details: "Learn modern software development methodologies, DevOps practices, and how to build scalable applications for various industries.",
    icon: <Code className="h-6 w-6 text-crimson" />,
  },
  {
    name: "Cloud Computing",
    slug: "btech-cloud-computing",
    description: "Distributed computing infrastructure",
    details: "Study cloud architectures, virtualization, containers, and how to design and deploy applications in cloud environments.",
    icon: <Server className="h-6 w-6 text-crimson" />,
  },
  {
    name: "Computer Networks",
    slug: "btech-computer-networks",
    description: "Network design and implementation",
    details: "Understand networking principles, protocols, security, and how to design and manage complex network infrastructures.",
    icon: <Network className="h-6 w-6 text-crimson" />,
  },
];

const facultyHighlights = [
  {
    name: "Dr. Maya Patel",
    role: "Department Chair, Cybersecurity",
    image: "/placeholder.svg?height=200&width=200",
    details: "With over 15 years of industry experience at leading tech companies, Dr. Patel brings practical knowledge and cutting-edge research to our cybersecurity program.",
  },
  {
    name: "Dr. Arun Sharma",
    role: "Professor, Artificial Intelligence",
    image: "/placeholder.svg?height=200&width=200",
    details: "An award-winning researcher in deep learning and computer vision with publications in top AI conferences and collaborations with tech giants.",
  },
  {
    name: "Dr. Samuel Chen",
    role: "Associate Professor, Data Science",
    image: "/placeholder.svg?height=200&width=200", 
    details: "Specializing in big data analytics and applied machine learning with a focus on solving real-world problems through data-driven approaches.",
  },
];

const labFacilities = [
  {
    name: "Cybersecurity Lab",
    description: "State-of-the-art facility for network security, penetration testing, and digital forensics",
    icon: <Lock className="h-6 w-6 text-crimson" />,
  },
  {
    name: "AI & ML Lab",
    description: "Equipped with high-performance GPUs for deep learning research and applications",
    icon: <Cpu className="h-6 w-6 text-crimson" />,
  },
  {
    name: "Data Science Center",
    description: "Specialized tools and infrastructure for big data processing and visualization",
    icon: <Database className="h-6 w-6 text-crimson" />,
  },
];

export default function ComputerScienceSchoolPage() {
  useParallaxEffect();

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Hero Section */}
          <section className="w-full py-16 md:py-24 lg:py-32 bg-gradient-to-b from-crimson/5 to-background relative overflow-hidden">
            <div className="absolute inset-0 overflow-hidden z-0">
              <div className="absolute -right-20 top-20 w-80 h-80 bg-crimson/5 rounded-full blur-3xl" />
              <div className="absolute left-1/4 bottom-0 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl" />
            </div>
            
            <div className="px-4 md:px-6 relative">
              <div className="grid gap-10 lg:grid-cols-2 lg:gap-16 items-center">
                <div className="flex flex-col justify-center space-y-6">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-crimson/10 text-crimson text-sm font-medium w-fit">
                    School of Computer Science
                  </div>
                  <div className="space-y-4">
                    <h1 className="heading-xl">
                      Shaping the Future of <span className="text-crimson">Technology</span>
                    </h1>
                    <p className="max-w-[600px] text-muted-foreground md:text-xl leading-relaxed">
                      Our School of Computer Science offers cutting-edge programs that prepare students for careers in the rapidly evolving tech industry.
                    </p>
                  </div>
                  <div className="flex flex-col gap-3 min-[400px]:flex-row pt-4">
                    <Button className="shadow-md transition-all hover:shadow-lg">
                      Explore Programs
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                    <Button variant="outline" className="border-crimson/20 hover:bg-crimson/5">
                      Schedule a Visit
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-center relative">
                  <div className="absolute -z-10 w-[120%] h-[120%] bg-gradient-to-tr from-crimson/10 via-blue-500/10 to-transparent rounded-full blur-2xl"></div>
                  <div className="relative w-full max-w-[550px] rounded-2xl overflow-hidden shadow-xl">
                    <img
                      src="/placeholder.svg?height=550&width=550"
                      width={550}
                      height={550}
                      alt="Computer Science students in a lab"
                      className="aspect-square w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-crimson/20 to-transparent mix-blend-multiply"></div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Department Overview */}
          <section className="w-full py-16 md:py-24 bg-light">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-crimson/10 text-crimson text-sm font-medium">
                    Department Overview
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Excellence in Computing Education and Research
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our School of Computer Science is committed to providing a comprehensive education that balances theoretical foundations with practical skills, preparing students for successful careers in the rapidly evolving tech industry.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  <div className="bg-background p-8 rounded-2xl shadow-sm flex flex-col items-center text-center">
                    <div className="h-16 w-16 rounded-full bg-crimson/10 flex items-center justify-center mb-4">
                      <Code className="h-8 w-8 text-crimson" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Innovative Curriculum</h3>
                    <p className="text-muted-foreground">
                      Our curriculum combines core computer science principles with emerging technologies and industry best practices.
                    </p>
                  </div>
                  
                  <div className="bg-background p-8 rounded-2xl shadow-sm flex flex-col items-center text-center">
                    <div className="h-16 w-16 rounded-full bg-crimson/10 flex items-center justify-center mb-4">
                      <Server className="h-8 w-8 text-crimson" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">State-of-the-Art Facilities</h3>
                    <p className="text-muted-foreground">
                      Modern labs equipped with the latest hardware and software tools for hands-on learning and research.
                    </p>
                  </div>

                  <div className="bg-background p-8 rounded-2xl shadow-sm flex flex-col items-center text-center">
                    <div className="h-16 w-16 rounded-full bg-crimson/10 flex items-center justify-center mb-4">
                      <Database className="h-8 w-8 text-crimson" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Industry Connections</h3>
                    <p className="text-muted-foreground">
                      Strong partnerships with leading tech companies for internships, projects, and employment opportunities.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Featured Program Section */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="grid items-center gap-10 lg:grid-cols-2 lg:gap-16 max-w-6xl mx-auto">
                <div className="flex items-center justify-center relative order-2 lg:order-1">
                  <div className="absolute -z-10 w-[120%] h-[120%] bg-gradient-to-tr from-crimson/10 via-gold/10 to-transparent rounded-full blur-2xl"></div>
                  <LazyImage
                    src="/placeholder.svg?height=450&width=450"
                    alt="Cybersecurity lab"
                    aspectRatio="aspect-square"
                    className="rounded-2xl shadow-lg"
                    width={450}
                    height={450}
                  />
                </div>

                <div className="flex flex-col justify-center space-y-6 order-1 lg:order-2">
                  <div className="space-y-4">
                    <div className="inline-flex items-center rounded-full px-3 py-1 text-sm font-medium bg-crimson text-white">
                      Featured Program
                    </div>
                    <h3 className="heading-md">{csFeaturedProgram.title}</h3>
                    <p className="text-muted-foreground text-lg leading-relaxed">
                      {csFeaturedProgram.details}
                    </p>
                  </div>
                  <ul className="grid gap-3 mt-2">
                    {csFeaturedProgram.points.map((point, index) => (
                      <li key={index} className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-crimson/10">
                          {point.icon}
                        </div>
                        <span className="font-medium">{point.text}</span>
                      </li>
                    ))}
                  </ul>
                  <div className="pt-2">
                    <Button asChild variant="outline" className="border-crimson/20 hover:bg-crimson/5">
                      <Link href={`/programs/${csFeaturedProgram.slug}`}>Learn More</Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Program Cards Section */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-crimson/10 text-crimson text-sm font-medium">
                    Our Programs
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Comprehensive Computing Education
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Explore our diverse range of programs designed to prepare you for the most in-demand careers in technology.
                    </p>
                  </div>
                </div>

                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 mt-12">
                  {[...csProgramCards, ...additionalPrograms].map((card, index) => (
                    <Link key={index} href={`/programs/${card.slug}`} className="block card-hover no-underline">
                      <Card className="h-full border-0 bg-light shadow-sm transition-all duration-300 hover:shadow-md">
                        <CardHeader>
                          <CardTitle className="text-xl text-foreground">{card.name}</CardTitle>
                          {card.description && <CardDescription className="text-muted-foreground/80">{card.description}</CardDescription>}
                        </CardHeader>
                        <CardContent>
                          <p className="text-muted-foreground leading-relaxed">
                            {card.details || `Explore various aspects of ${card.name}.`}
                          </p>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Research Areas */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-background to-light/50">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-crimson/10 text-crimson text-sm font-medium">
                    Research
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Advancing Computing Research
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our faculty and students collaborate on cutting-edge research projects, addressing real-world challenges through technological innovation.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-12">
                  <div className="bg-background p-6 rounded-xl border border-border/50 hover:shadow-md transition-all duration-300">
                    <h3 className="text-xl font-bold mb-3">Artificial Intelligence & Machine Learning</h3>
                    <p className="text-muted-foreground mb-4">
                      Research in neural networks, deep learning, computer vision, natural language processing, and reinforcement learning.
                    </p>
                    <Link href="/research/ai-ml" className="inline-flex items-center text-crimson hover:underline">
                      Learn more <ChevronRight className="h-4 w-4 ml-1" />
                    </Link>
                  </div>
                  
                  <div className="bg-background p-6 rounded-xl border border-border/50 hover:shadow-md transition-all duration-300">
                    <h3 className="text-xl font-bold mb-3">Cybersecurity & Privacy</h3>
                    <p className="text-muted-foreground mb-4">
                      Research in network security, cryptography, secure systems, privacy-preserving technologies, and digital forensics.
                    </p>
                    <Link href="/research/cybersecurity" className="inline-flex items-center text-crimson hover:underline">
                      Learn more <ChevronRight className="h-4 w-4 ml-1" />
                    </Link>
                  </div>
                  
                  <div className="bg-background p-6 rounded-xl border border-border/50 hover:shadow-md transition-all duration-300">
                    <h3 className="text-xl font-bold mb-3">Data Science & Analytics</h3>
                    <p className="text-muted-foreground mb-4">
                      Research in big data processing, data mining, statistical modeling, and predictive analytics across various domains.
                    </p>
                    <Link href="/research/data-science" className="inline-flex items-center text-crimson hover:underline">
                      Learn more <ChevronRight className="h-4 w-4 ml-1" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Faculty Highlights */}
          <section className="w-full py-16 md:py-24 bg-light">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-crimson/10 text-crimson text-sm font-medium">
                    Our Faculty
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      Learn from Industry Experts
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our faculty members bring a wealth of industry experience and academic expertise to provide students with a world-class education.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  {facultyHighlights.map((faculty, index) => (
                    <div key={index} className="bg-background rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
                      <div className="h-48 overflow-hidden">
                        <img 
                          src={faculty.image} 
                          alt={faculty.name} 
                          className="w-full h-full object-cover object-center"
                        />
                      </div>
                      <div className="p-6">
                        <h3 className="text-xl font-bold mb-1">{faculty.name}</h3>
                        <p className="text-crimson text-sm font-medium mb-3">{faculty.role}</p>
                        <p className="text-muted-foreground">{faculty.details}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="text-center mt-12">
                  <Button asChild variant="outline" className="border-crimson/20 hover:bg-crimson/5">
                    <Link href="/faculty">View All Faculty</Link>
                  </Button>
                </div>
              </div>
            </div>
          </section>

          {/* Lab Facilities */}
          <section className="w-full py-16 md:py-24 bg-background">
            <div className="px-4 md:px-6">
              <div className="max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-crimson/10 text-crimson text-sm font-medium">
                    Our Facilities
                  </div>
                  <div className="space-y-4">
                    <h2 className="heading-lg">
                      State-of-the-Art Labs & Infrastructure
                    </h2>
                    <p className="text-muted-foreground text-lg leading-relaxed max-w-3xl mx-auto">
                      Our modern labs and facilities provide students with hands-on experience using the latest technologies and tools.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                  {labFacilities.map((lab, index) => (
                    <div key={index} className="bg-light p-8 rounded-xl flex flex-col items-center text-center">
                      <div className="h-16 w-16 rounded-full bg-crimson/10 flex items-center justify-center mb-4">
                        {lab.icon}
                      </div>
                      <h3 className="text-xl font-bold mb-3">{lab.name}</h3>
                      <p className="text-muted-foreground">{lab.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section className="w-full py-16 md:py-24 bg-gradient-to-b from-background to-crimson/5">
            <div className="px-4 md:px-6">
              <div className="max-w-5xl mx-auto bg-background rounded-2xl shadow-lg overflow-hidden">
                <div className="grid grid-cols-1 lg:grid-cols-2">
                  <div className="p-8 md:p-12 flex flex-col justify-center">
                    <h2 className="text-3xl font-bold mb-4">Ready to Shape the Future?</h2>
                    <p className="text-muted-foreground mb-6">
                      Join our School of Computer Science and start your journey toward a rewarding career in technology. Applications for the upcoming academic year are now open.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <Button className="shadow-md">
                        Apply Now
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                      <Button variant="outline" className="border-crimson/20 hover:bg-crimson/5">
                        Request Information
                      </Button>
                    </div>
                  </div>
                  <div className="hidden lg:block relative">
                    <img
                      src="/placeholder.svg?height=400&width=500"
                      alt="Students collaborating"
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-crimson/20 to-transparent mix-blend-multiply"></div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
        <StickyCTA />
      </div>
    </PageTransition>
  );
} 