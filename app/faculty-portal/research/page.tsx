import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { Beaker, Plus, Edit, Trash2, Target } from "lucide-react"

async function getFacultyResearchAreas(userId: string) {
  const facultyProfile = await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      researchAreas: {
        orderBy: { createdAt: 'desc' }
      }
    }
  })
  return facultyProfile?.researchAreas || []
}

export default async function ResearchAreasPage() {
  const user = await requireFaculty()
  const researchAreas = await getFacultyResearchAreas(user.id)

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Research Areas</h1>
          <p className="text-gray-600">Manage your research interests and areas of expertise</p>
        </div>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          Add Research Area
        </Button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Research Areas</CardTitle>
            <Beaker className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{researchAreas.length}</div>
            <p className="text-xs text-muted-foreground">Areas of expertise</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Profile Completion</CardTitle>
            <Target className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {researchAreas.length >= 3 ? '100%' : `${Math.round((researchAreas.length / 3) * 100)}%`}
            </div>
            <p className="text-xs text-muted-foreground">
              {researchAreas.length >= 3 ? 'Complete' : `Add ${3 - researchAreas.length} more areas`}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Research Areas List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Research Areas</CardTitle>
          <CardDescription>
            Define your research interests and areas of expertise to help students and collaborators find you
          </CardDescription>
        </CardHeader>
        <CardContent>
          {researchAreas.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {researchAreas.map((area) => (
                <div key={area.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg mb-2">{area.areaName}</h3>
                      {area.description && (
                        <p className="text-sm text-gray-600 mb-3">
                          {area.description}
                        </p>
                      )}
                      <p className="text-xs text-gray-500">
                        Added {new Date(area.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Beaker className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No research areas yet</h3>
              <p className="text-gray-600 mb-4">
                Add your research areas to help others understand your expertise and interests.
              </p>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Research Area
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Research Area Form */}
      <Card>
        <CardHeader>
          <CardTitle>Add New Research Area</CardTitle>
          <CardDescription>
            Define a new area of research interest or expertise
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="areaName">Research Area Name *</Label>
            <Input 
              id="areaName" 
              placeholder="e.g., Machine Learning, Artificial Intelligence, Data Science" 
            />
            <p className="text-xs text-gray-500 mt-1">
              Enter a specific research area or field of study
            </p>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea 
              id="description" 
              rows={4}
              placeholder="Describe your research interests, methodologies, and focus areas within this field..."
            />
            <p className="text-xs text-gray-500 mt-1">
              Provide details about your specific interests and expertise in this area
            </p>
          </div>

          <div className="flex justify-end space-x-4">
            <Button variant="outline">Cancel</Button>
            <Button>Add Research Area</Button>
          </div>
        </CardContent>
      </Card>

      {/* Suggested Research Areas */}
      <Card>
        <CardHeader>
          <CardTitle>Suggested Research Areas</CardTitle>
          <CardDescription>
            Common research areas in your field
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {[
              'Machine Learning',
              'Artificial Intelligence',
              'Data Science',
              'Computer Vision',
              'Natural Language Processing',
              'Robotics',
              'Cybersecurity',
              'Software Engineering',
              'Human-Computer Interaction',
              'Database Systems',
              'Distributed Systems',
              'Algorithms',
              'Computational Biology',
              'Quantum Computing',
              'Blockchain Technology',
              'Internet of Things'
            ].map((suggestion) => (
              <Button
                key={suggestion}
                variant="outline"
                size="sm"
                className="justify-start text-left h-auto py-2 px-3"
              >
                <Plus className="w-3 h-3 mr-2 flex-shrink-0" />
                <span className="text-xs">{suggestion}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Tips */}
      <Card>
        <CardHeader>
          <CardTitle>Tips for Research Areas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600 space-y-2">
            <p>• <strong>Be specific:</strong> Instead of "Computer Science," use "Machine Learning in Healthcare"</p>
            <p>• <strong>Include methodologies:</strong> Mention specific techniques or approaches you use</p>
            <p>• <strong>Update regularly:</strong> Add new areas as your research evolves</p>
            <p>• <strong>Use keywords:</strong> Include terms that students and collaborators might search for</p>
            <p>• <strong>Aim for 3-5 areas:</strong> This provides good coverage without being overwhelming</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
