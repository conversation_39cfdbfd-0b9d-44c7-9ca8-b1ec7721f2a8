import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { GraduationCap, Plus, Edit, Trash2, Award } from "lucide-react"

async function getFacultyEducation(userId: string) {
  const facultyProfile = await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      education: {
        orderBy: { year: 'desc' }
      }
    }
  })
  return facultyProfile?.education || []
}

export default async function EducationPage() {
  const user = await requireFaculty()
  const education = await getFacultyEducation(user.id)

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Education</h1>
          <p className="text-gray-600">Manage your educational background and academic degrees</p>
        </div>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          Add Degree
        </Button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Degrees</CardTitle>
            <GraduationCap className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{education.length}</div>
            <p className="text-xs text-muted-foreground">Academic degrees</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Highest Degree</CardTitle>
            <Award className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {education.length > 0 ? education[0].degree.split(' ')[0] : 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">
              {education.length > 0 ? education[0].institution : 'No degrees added'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Latest Degree</CardTitle>
            <GraduationCap className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {education.length > 0 && education[0].year ? education[0].year : 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">Year completed</p>
          </CardContent>
        </Card>
      </div>

      {/* Education Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Educational Background</CardTitle>
          <CardDescription>
            Your academic degrees and educational achievements
          </CardDescription>
        </CardHeader>
        <CardContent>
          {education.length > 0 ? (
            <div className="space-y-4">
              {education.map((edu) => (
                <div key={edu.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <GraduationCap className="w-5 h-5 text-blue-600" />
                        <h3 className="font-semibold text-lg">{edu.degree}</h3>
                        {edu.year && (
                          <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                            {edu.year}
                          </span>
                        )}
                      </div>
                      <p className="text-gray-600 mb-1">
                        <strong>Institution:</strong> {edu.institution}
                      </p>
                      {edu.field && (
                        <p className="text-gray-600 mb-1">
                          <strong>Field of Study:</strong> {edu.field}
                        </p>
                      )}
                      <p className="text-xs text-gray-500">
                        Added {new Date(edu.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <GraduationCap className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No education records yet</h3>
              <p className="text-gray-600 mb-4">
                Add your educational background to showcase your academic credentials.
              </p>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Degree
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Education Form */}
      <Card>
        <CardHeader>
          <CardTitle>Add New Degree</CardTitle>
          <CardDescription>
            Add a new academic degree or educational achievement
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="degree">Degree *</Label>
            <Input 
              id="degree" 
              placeholder="e.g., Ph.D. in Computer Science, M.S. in Engineering" 
            />
            <p className="text-xs text-gray-500 mt-1">
              Include the full degree title and field of study
            </p>
          </div>

          <div>
            <Label htmlFor="institution">Institution *</Label>
            <Input 
              id="institution" 
              placeholder="e.g., Stanford University, MIT, University of California" 
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="field">Field of Study</Label>
              <Input 
                id="field" 
                placeholder="e.g., Computer Science, Electrical Engineering" 
              />
            </div>
            <div>
              <Label htmlFor="year">Year Completed</Label>
              <Input 
                id="year" 
                type="number" 
                placeholder="2020" 
                min="1950" 
                max="2030" 
              />
            </div>
          </div>

          <div className="flex justify-end space-x-4">
            <Button variant="outline">Cancel</Button>
            <Button>Add Degree</Button>
          </div>
        </CardContent>
      </Card>

      {/* Common Degrees */}
      <Card>
        <CardHeader>
          <CardTitle>Common Degree Types</CardTitle>
          <CardDescription>
            Quick options for common academic degrees
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {[
              'Ph.D.',
              'M.S./M.Sc.',
              'M.A.',
              'B.S./B.Sc.',
              'B.A.',
              'M.B.A.',
              'M.Ed.',
              'Ed.D.',
              'J.D.',
              'M.D.',
              'Postdoc',
              'Certificate'
            ].map((degreeType) => (
              <Button
                key={degreeType}
                variant="outline"
                size="sm"
                className="justify-start text-left h-auto py-2 px-3"
              >
                <Plus className="w-3 h-3 mr-2 flex-shrink-0" />
                <span className="text-xs">{degreeType}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Tips */}
      <Card>
        <CardHeader>
          <CardTitle>Tips for Education Records</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600 space-y-2">
            <p>• <strong>Be complete:</strong> Include all relevant degrees and certifications</p>
            <p>• <strong>Use full names:</strong> Write out complete institution names</p>
            <p>• <strong>Include field:</strong> Specify your area of study for each degree</p>
            <p>• <strong>Order matters:</strong> List degrees in reverse chronological order (newest first)</p>
            <p>• <strong>Add postdocs:</strong> Include postdoctoral positions as educational experiences</p>
            <p>• <strong>Certifications:</strong> Include relevant professional certifications</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
