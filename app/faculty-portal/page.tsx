import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { 
  User, 
  FileText, 
  BookOpen, 
  Calendar,
  Clock,
  Beaker,
  Users,
  TrendingUp,
  <PERSON>
} from "lucide-react"
import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import Link from "next/link"

async function getFacultyDashboardData(userId: string) {
  const facultyProfile = await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      user: {
        include: {
          profile: true
        }
      },
      department: true,
      publications: {
        take: 5,
        orderBy: { year: 'desc' }
      },
      researchProjects: {
        take: 3,
        orderBy: { createdAt: 'desc' }
      },
      classes: {
        take: 3,
        include: {
          course: true
        },
        orderBy: { createdAt: 'desc' }
      },
      officeHours: {
        where: { isActive: true },
        take: 5
      }
    }
  })

  const upcomingBookings = await prisma.officeHourBooking.findMany({
    where: {
      officeHour: {
        facultyId: facultyProfile?.id
      },
      date: {
        gte: new Date()
      },
      status: 'CONFIRMED'
    },
    include: {
      student: {
        select: {
          name: true,
          email: true
        }
      }
    },
    take: 5,
    orderBy: { date: 'asc' }
  })

  return {
    facultyProfile,
    upcomingBookings
  }
}

export default async function FacultyDashboard() {
  const user = await requireFaculty()
  const { facultyProfile, upcomingBookings } = await getFacultyDashboardData(user.id)

  if (!facultyProfile) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Faculty Profile Not Found</CardTitle>
            <CardDescription>
              Your faculty profile hasn't been set up yet. Please contact the administrator.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  const stats = [
    {
      title: "Publications",
      value: facultyProfile.publications.length,
      description: "Research publications",
      icon: FileText,
      color: "text-blue-600"
    },
    {
      title: "Research Projects",
      value: facultyProfile.researchProjects.length,
      description: "Active projects",
      icon: Beaker,
      color: "text-green-600"
    },
    {
      title: "Courses",
      value: facultyProfile.classes.length,
      description: "Teaching assignments",
      icon: BookOpen,
      color: "text-purple-600"
    },
    {
      title: "Office Hours",
      value: facultyProfile.officeHours.length,
      description: "Weekly slots",
      icon: Clock,
      color: "text-orange-600"
    }
  ]

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {facultyProfile.user.name}
        </h1>
        <p className="text-gray-600">
          {facultyProfile.title} • {facultyProfile.department.name}
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Publications */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Publications</CardTitle>
            <CardDescription>
              Your latest research publications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {facultyProfile.publications.length > 0 ? (
                facultyProfile.publications.map((pub) => (
                  <div key={pub.id} className="border-l-2 border-blue-200 pl-4">
                    <h4 className="font-medium text-sm">{pub.title}</h4>
                    <p className="text-xs text-gray-500">
                      {pub.journal} • {pub.year}
                    </p>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500">No publications yet</p>
              )}
            </div>
            <div className="mt-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/faculty-portal/publications">
                  <FileText className="w-4 h-4 mr-2" />
                  Manage Publications
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Office Hour Bookings */}
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Appointments</CardTitle>
            <CardDescription>
              Student office hour bookings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingBookings.length > 0 ? (
                upcomingBookings.map((booking) => (
                  <div key={booking.id} className="flex items-center space-x-4">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {booking.student.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(booking.date).toLocaleDateString()} • {booking.startTime}
                      </p>
                    </div>
                    <Bell className="w-4 h-4 text-gray-400" />
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500">No upcoming appointments</p>
              )}
            </div>
            <div className="mt-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/faculty-portal/office-hours">
                  <Clock className="w-4 h-4 mr-2" />
                  Manage Office Hours
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Research Projects */}
        <Card>
          <CardHeader>
            <CardTitle>Research Projects</CardTitle>
            <CardDescription>
              Your active research initiatives
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {facultyProfile.researchProjects.length > 0 ? (
                facultyProfile.researchProjects.map((project) => (
                  <div key={project.id} className="border-l-2 border-green-200 pl-4">
                    <h4 className="font-medium text-sm">{project.title}</h4>
                    <p className="text-xs text-gray-500 capitalize">
                      {project.status.toLowerCase()} • {project.positions} positions
                    </p>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500">No research projects yet</p>
              )}
            </div>
            <div className="mt-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/faculty-portal/research">
                  <Beaker className="w-4 h-4 mr-2" />
                  Manage Research
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-3">
              <Button variant="outline" size="sm" asChild>
                <Link href="/faculty-portal/profile">
                  <User className="w-4 h-4 mr-2" />
                  Update Profile
                </Link>
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href="/faculty-portal/courses">
                  <BookOpen className="w-4 h-4 mr-2" />
                  Manage Courses
                </Link>
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href="/faculty-portal/schedule">
                  <Calendar className="w-4 h-4 mr-2" />
                  View Schedule
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
