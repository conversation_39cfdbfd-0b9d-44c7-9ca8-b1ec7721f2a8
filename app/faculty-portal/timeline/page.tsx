import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { Calendar, Plus, Edit, Trash2, Award, Briefcase, GraduationCap, FileText } from "lucide-react"

async function getFacultyTimeline(userId: string) {
  const facultyProfile = await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      timeline: {
        orderBy: { year: 'desc' }
      }
    }
  })
  return facultyProfile?.timeline || []
}

const timelineIcons = {
  EDUCATION: GraduationCap,
  POSITION: Briefcase,
  AWARD: Award,
  PUBLICATION: FileText
}

const timelineColors = {
  EDUCATION: 'text-blue-600 bg-blue-100',
  POSITION: 'text-green-600 bg-green-100',
  AWARD: 'text-yellow-600 bg-yellow-100',
  PUBLICATION: 'text-purple-600 bg-purple-100'
}

export default async function TimelinePage() {
  const user = await requireFaculty()
  const timeline = await getFacultyTimeline(user.id)

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Career Timeline</h1>
          <p className="text-gray-600">Manage your career milestones and achievements</p>
        </div>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          Add Event
        </Button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {Object.entries(timelineColors).map(([type, colorClass]) => {
          const count = timeline.filter(item => item.type === type).length
          const Icon = timelineIcons[type as keyof typeof timelineIcons]
          return (
            <Card key={type}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium capitalize">
                  {type.toLowerCase()}s
                </CardTitle>
                <Icon className={`h-4 w-4 ${colorClass.split(' ')[0]}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{count}</div>
                <p className="text-xs text-muted-foreground">
                  {type.toLowerCase()} events
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Your Career Timeline</CardTitle>
          <CardDescription>
            A chronological view of your career milestones and achievements
          </CardDescription>
        </CardHeader>
        <CardContent>
          {timeline.length > 0 ? (
            <div className="space-y-6">
              {timeline.map((event, index) => {
                const Icon = timelineIcons[event.type as keyof typeof timelineIcons]
                const colorClass = timelineColors[event.type as keyof typeof timelineColors]
                
                return (
                  <div key={event.id} className="relative">
                    {/* Timeline line */}
                    {index < timeline.length - 1 && (
                      <div className="absolute left-6 top-12 w-0.5 h-16 bg-gray-200"></div>
                    )}
                    
                    <div className="flex items-start space-x-4">
                      {/* Timeline icon */}
                      <div className={`flex-shrink-0 w-12 h-12 rounded-full ${colorClass} flex items-center justify-center`}>
                        <Icon className="w-6 h-6" />
                      </div>
                      
                      {/* Timeline content */}
                      <div className="flex-1 border rounded-lg p-4 hover:bg-gray-50">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className="font-semibold text-lg">{event.title}</h3>
                              <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                                {event.year}
                              </span>
                              <span className={`text-xs px-2 py-1 rounded-full capitalize ${colorClass}`}>
                                {event.type.toLowerCase()}
                              </span>
                            </div>
                            <p className="text-gray-600 mb-2">{event.description}</p>
                            <p className="text-xs text-gray-500">
                              Added {new Date(event.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2 ml-4">
                            <Button variant="outline" size="sm">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No timeline events yet</h3>
              <p className="text-gray-600 mb-4">
                Start building your career timeline by adding your first milestone.
              </p>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Event
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Timeline Event Form */}
      <Card>
        <CardHeader>
          <CardTitle>Add New Timeline Event</CardTitle>
          <CardDescription>
            Add a new milestone or achievement to your career timeline
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="title">Event Title *</Label>
            <Input 
              id="title" 
              placeholder="e.g., Promoted to Associate Professor, Received Best Paper Award" 
            />
          </div>

          <div>
            <Label htmlFor="year">Year *</Label>
            <Input 
              id="year" 
              placeholder="e.g., 2023, 2020-2022" 
            />
            <p className="text-xs text-gray-500 mt-1">
              Can be a single year or a range (e.g., "2020-2022")
            </p>
          </div>

          <div>
            <Label htmlFor="type">Event Type *</Label>
            <select 
              id="type" 
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select event type</option>
              <option value="EDUCATION">Education</option>
              <option value="POSITION">Position/Job</option>
              <option value="AWARD">Award/Recognition</option>
              <option value="PUBLICATION">Publication/Research</option>
            </select>
          </div>

          <div>
            <Label htmlFor="description">Description *</Label>
            <Textarea 
              id="description" 
              rows={3}
              placeholder="Describe the event, achievement, or milestone in detail..."
            />
          </div>

          <div className="flex justify-end space-x-4">
            <Button variant="outline">Cancel</Button>
            <Button>Add Event</Button>
          </div>
        </CardContent>
      </Card>

      {/* Event Type Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Event Type Examples</CardTitle>
          <CardDescription>
            Common types of career events you might want to add
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-green-600 mb-2 flex items-center">
                <Briefcase className="w-4 h-4 mr-2" />
                Position Events
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Started as Assistant Professor</li>
                <li>• Promoted to Associate Professor</li>
                <li>• Appointed Department Chair</li>
                <li>• Joined as Postdoctoral Researcher</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-yellow-600 mb-2 flex items-center">
                <Award className="w-4 h-4 mr-2" />
                Award Events
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Received Teaching Excellence Award</li>
                <li>• Won Best Paper Award</li>
                <li>• Granted NSF Fellowship</li>
                <li>• Named Distinguished Researcher</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-blue-600 mb-2 flex items-center">
                <GraduationCap className="w-4 h-4 mr-2" />
                Education Events
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Completed Ph.D. in Computer Science</li>
                <li>• Finished Postdoctoral Fellowship</li>
                <li>• Earned Professional Certification</li>
                <li>• Completed Sabbatical Research</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-purple-600 mb-2 flex items-center">
                <FileText className="w-4 h-4 mr-2" />
                Publication Events
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Published first journal paper</li>
                <li>• Reached 100 citations milestone</li>
                <li>• Published book or monograph</li>
                <li>• Filed patent application</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
