import { requireFaculty } from "@/lib/auth-utils"
import { FacultySidebar } from "@/components/faculty/FacultySidebar"
import { FacultyHeader } from "@/components/faculty/FacultyHeader"

export default async function FacultyPortalLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Require faculty role to access this layout
  const user = await requireFaculty()

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <FacultySidebar user={user} />
      
      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <FacultyHeader user={user} />
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
          {children}
        </main>
      </div>
    </div>
  )
}
