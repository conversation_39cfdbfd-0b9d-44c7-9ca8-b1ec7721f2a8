import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { FileText, Plus, Edit, Trash2, ExternalLink } from "lucide-react"

async function getFacultyPublications(userId: string) {
  const facultyProfile = await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      publications: {
        orderBy: { year: 'desc' }
      }
    }
  })
  return facultyProfile?.publications || []
}

export default async function PublicationsPage() {
  const user = await requireFaculty()
  const publications = await getFacultyPublications(user.id)

  return (
    <div className="space-y-6">
      {/* <PERSON> */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Publications</h1>
          <p className="text-gray-600">Manage your academic publications and research papers</p>
        </div>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          Add Publication
        </Button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Publications</CardTitle>
            <FileText className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{publications.length}</div>
            <p className="text-xs text-muted-foreground">Academic papers</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Citations</CardTitle>
            <FileText className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {publications.reduce((sum, pub) => sum + pub.citationCount, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Citation count</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Publications</CardTitle>
            <FileText className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {publications.filter(pub => pub.year >= new Date().getFullYear() - 2).length}
            </div>
            <p className="text-xs text-muted-foreground">Last 2 years</p>
          </CardContent>
        </Card>
      </div>

      {/* Publications List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Publications</CardTitle>
          <CardDescription>
            Manage your academic publications, research papers, and scholarly works
          </CardDescription>
        </CardHeader>
        <CardContent>
          {publications.length > 0 ? (
            <div className="space-y-4">
              {publications.map((publication) => (
                <div key={publication.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg mb-2">{publication.title}</h3>
                      <p className="text-sm text-gray-600 mb-2">
                        <strong>Authors: <AUTHORS>
                      </p>
                      <p className="text-sm text-gray-600 mb-2">
                        <strong>Journal:</strong> {publication.journal} • <strong>Year:</strong> {publication.year}
                      </p>
                      {publication.abstract && (
                        <p className="text-sm text-gray-700 mb-2">
                          <strong>Abstract:</strong> {publication.abstract.substring(0, 200)}
                          {publication.abstract.length > 200 && '...'}
                        </p>
                      )}
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>Citations: {publication.citationCount}</span>
                        {publication.tags.length > 0 && (
                          <span>Tags: {publication.tags.join(', ')}</span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      {publication.link && (
                        <Button variant="outline" size="sm" asChild>
                          <a href={publication.link} target="_blank" rel="noopener noreferrer">
                            <ExternalLink className="w-4 h-4" />
                          </a>
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No publications yet</h3>
              <p className="text-gray-600 mb-4">
                Start building your academic profile by adding your first publication.
              </p>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Publication
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Publication Form */}
      <Card>
        <CardHeader>
          <CardTitle>Add New Publication</CardTitle>
          <CardDescription>
            Add a new academic publication to your profile
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="title">Title *</Label>
            <Input id="title" placeholder="Enter publication title" />
          </div>

          <div>
            <Label htmlFor="authors">Authors *</Label>
            <Input id="authors" placeholder="Enter authors (comma-separated)" />
            <p className="text-xs text-gray-500 mt-1">
              List all authors separated by commas
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="journal">Journal/Conference *</Label>
              <Input id="journal" placeholder="Journal or conference name" />
            </div>
            <div>
              <Label htmlFor="year">Year *</Label>
              <Input id="year" type="number" placeholder="2024" min="1900" max="2030" />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="citations">Citation Count</Label>
              <Input id="citations" type="number" placeholder="0" min="0" />
            </div>
            <div>
              <Label htmlFor="link">Publication Link</Label>
              <Input id="link" type="url" placeholder="https://..." />
            </div>
          </div>

          <div>
            <Label htmlFor="abstract">Abstract</Label>
            <Textarea 
              id="abstract" 
              rows={4}
              placeholder="Enter the abstract or summary of your publication..."
            />
          </div>

          <div>
            <Label htmlFor="tags">Tags</Label>
            <Input id="tags" placeholder="machine learning, AI, computer vision" />
            <p className="text-xs text-gray-500 mt-1">
              Add relevant tags separated by commas
            </p>
          </div>

          <div className="flex justify-end space-x-4">
            <Button variant="outline">Cancel</Button>
            <Button>Add Publication</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
