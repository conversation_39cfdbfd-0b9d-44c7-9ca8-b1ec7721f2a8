import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for education
const educationSchema = z.object({
  degree: z.string().min(1, 'Degree is required'),
  institution: z.string().min(1, 'Institution is required'),
  field: z.string().optional(),
  year: z.number().min(1950).max(2030).optional()
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = educationSchema.parse(body)

    // Verify the education record belongs to the current faculty member
    const education = await prisma.facultyEducation.findFirst({
      where: {
        id: params.id,
        facultyProfile: {
          userId: session.user.id
        }
      }
    })

    if (!education) {
      return NextResponse.json({ error: 'Education record not found' }, { status: 404 })
    }

    // Update education record
    const updatedEducation = await prisma.facultyEducation.update({
      where: { id: params.id },
      data: {
        ...validatedData,
        updatedAt: new Date()
      }
    })

    return NextResponse.json({ 
      message: 'Education record updated successfully',
      education: updatedEducation 
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation error',
        details: error.errors 
      }, { status: 400 })
    }

    console.error('Error updating education record:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the education record belongs to the current faculty member
    const education = await prisma.facultyEducation.findFirst({
      where: {
        id: params.id,
        facultyProfile: {
          userId: session.user.id
        }
      }
    })

    if (!education) {
      return NextResponse.json({ error: 'Education record not found' }, { status: 404 })
    }

    // Delete education record
    await prisma.facultyEducation.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ 
      message: 'Education record deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting education record:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
