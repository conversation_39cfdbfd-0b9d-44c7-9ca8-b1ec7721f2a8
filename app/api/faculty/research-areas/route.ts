import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for research areas
const researchAreaSchema = z.object({
  areaName: z.string().min(1, 'Research area name is required'),
  description: z.string().optional()
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const facultyProfile = await prisma.facultyProfile.findUnique({
      where: { userId: session.user.id },
      include: {
        researchAreas: {
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    if (!facultyProfile) {
      return NextResponse.json({ error: 'Faculty profile not found' }, { status: 404 })
    }

    return NextResponse.json({ researchAreas: facultyProfile.researchAreas })
  } catch (error) {
    console.error('Error fetching research areas:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = researchAreaSchema.parse(body)

    // Get faculty profile
    const facultyProfile = await prisma.facultyProfile.findUnique({
      where: { userId: session.user.id }
    })

    if (!facultyProfile) {
      return NextResponse.json({ error: 'Faculty profile not found' }, { status: 404 })
    }

    // Create new research area
    const researchArea = await prisma.facultyResearchArea.create({
      data: {
        ...validatedData,
        facultyProfileId: facultyProfile.id
      }
    })

    return NextResponse.json({ 
      message: 'Research area added successfully',
      researchArea 
    }, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation error',
        details: error.errors 
      }, { status: 400 })
    }

    console.error('Error creating research area:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
