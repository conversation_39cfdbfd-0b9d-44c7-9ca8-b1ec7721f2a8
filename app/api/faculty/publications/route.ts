import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for publications
const publicationSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  authors: z.array(z.string()).min(1, 'At least one author is required'),
  journal: z.string().min(1, 'Journal/Conference is required'),
  year: z.number().min(1900).max(2030),
  citationCount: z.number().min(0).default(0),
  link: z.string().url().optional().or(z.literal('')),
  abstract: z.string().optional(),
  tags: z.array(z.string()).default([])
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const facultyProfile = await prisma.facultyProfile.findUnique({
      where: { userId: session.user.id },
      include: {
        publications: {
          orderBy: { year: 'desc' }
        }
      }
    })

    if (!facultyProfile) {
      return NextResponse.json({ error: 'Faculty profile not found' }, { status: 404 })
    }

    return NextResponse.json({ publications: facultyProfile.publications })
  } catch (error) {
    console.error('Error fetching publications:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = publicationSchema.parse(body)

    // Get faculty profile
    const facultyProfile = await prisma.facultyProfile.findUnique({
      where: { userId: session.user.id }
    })

    if (!facultyProfile) {
      return NextResponse.json({ error: 'Faculty profile not found' }, { status: 404 })
    }

    // Create new publication
    const publication = await prisma.facultyPublication.create({
      data: {
        ...validatedData,
        facultyProfileId: facultyProfile.id
      }
    })

    return NextResponse.json({ 
      message: 'Publication added successfully',
      publication 
    }, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation error',
        details: error.errors 
      }, { status: 400 })
    }

    console.error('Error creating publication:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
