'use client'

import React, { useRef, useEffect, useState } from "react"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import { 
  BookOpen, 
  Sparkles, 
  ShieldCheck, 
  Globe, 
  Users,
  ChevronRight,
  ArrowUp,
  CheckCircle2
} from "lucide-react"
import Link from "next/link"

export default function ValuesPage() {
  const [activeTab, setActiveTab] = useState("excellence");
  
  // Value sections with their ids for navigation
  const valueSections = [
    { 
      id: "excellence", 
      title: "Excellence and Deep Inquiry", 
      icon: <BookOpen className="h-6 w-6" />,
      headline: "We pursue the highest standards in teaching, research, and personal growth.",
      content: [
        "We believe that true excellence is rooted in curiosity, rigorous thinking, and a relentless drive to understand the world more deeply. We encourage our community to ask bold questions, challenge assumptions, and seek knowledge that leads to meaningful impact—locally and globally.",
        "We celebrate originality, intellectual courage, and the joy of discovery. We value both the mastery of technology and the wisdom to use it ethically and responsibly."
      ],
      example: "Our faculty-led research initiative brings students and professors together to tackle challenging problems, encouraging deep inquiry and pushing the boundaries of knowledge in their disciplines.",
      color: "primary",
      borderColor: "primary/30",
      bgColor: "primary/10"
    },
    { 
      id: "creativity", 
      title: "Creativity and Interdisciplinary Spirit", 
      icon: <Sparkles className="h-6 w-6" />,
      headline: "We thrive at the intersections—where disciplines meet, ideas collide, and new solutions emerge.",
      content: [
        "We foster an environment where creative problem-solving is second nature, and where students and faculty are empowered to cross boundaries, experiment, and innovate. We believe that the most pressing challenges require not just technical skill, but also imagination, empathy, and the ability to see connections others might miss.",
        "We embrace the unconventional, encourage playful exploration, and see failure as a vital step on the path to growth."
      ],
      example: "Our interdisciplinary projects bring together students from computer science, agriculture, and business to develop technology solutions for sustainable farming practices, blending expertise to solve real-world problems.",
      color: "gold",
      borderColor: "gold/40",
      bgColor: "gold/15"
    },
    { 
      id: "integrity", 
      title: "Integrity and Ethical Leadership", 
      icon: <ShieldCheck className="h-6 w-6" />,
      headline: "We hold ourselves to the highest standards of honesty, transparency, and ethical conduct.",
      content: [
        "We believe that technological advancement must be guided by a strong moral compass. Our community is committed to acting with integrity, taking responsibility for our actions, and making decisions that serve the greater good.",
        "We speak openly about our challenges, learn from our mistakes, and strive to build trust in all our relationships."
      ],
      example: "Our Ethics in Technology course integrates ethical considerations into technical education, preparing students to make responsible decisions in their future roles as industry leaders and innovators.",
      color: "crimson",
      borderColor: "crimson/30",
      bgColor: "crimson/10"
    },
    { 
      id: "global", 
      title: "Global Mindset and Adaptability", 
      icon: <Globe className="h-6 w-6" />,
      headline: "We prepare our graduates to thrive in a rapidly changing, interconnected world.",
      content: [
        "We cultivate cross-cultural understanding, global awareness, and flexibility to adapt to new environments and ideas. We encourage our community to look beyond borders, learn from diverse perspectives, and contribute solutions to global challenges.",
        "We see ourselves as part of a worldwide network of learners, innovators, and changemakers."
      ],
      example: "Our planned study abroad and international collaboration programs will connect students with peers around the world, fostering global perspectives and preparing them for careers in an interconnected global economy.",
      color: "blue-500",
      borderColor: "blue-400/30",
      bgColor: "blue-400/10"
    },
    { 
      id: "belonging", 
      title: "Belonging and Community", 
      icon: <Users className="h-6 w-6" />,
      headline: "We are committed to building a welcoming, inclusive, and supportive community where everyone can flourish.",
      content: [
        "We value diversity in all its forms and believe that every individual's voice and experience enriches our collective journey. We care for one another's wellbeing—mind, body, and spirit—and strive to create an environment where all feel respected, valued, and empowered to reach their full potential.",
        "We recognize that our strength lies in our unity, and we share the responsibility to use our talents wisely, for the benefit of humanity and the planet."
      ],
      example: "Our community engagement initiatives and student-led diversity programs create a campus where different perspectives are valued and all students feel they belong and can contribute meaningfully.",
      color: "emerald-500",
      borderColor: "emerald-400/30",
      bgColor: "emerald-400/10"
    },
  ];
  
  // Find the currently active section data
  const activeSection = valueSections.find(section => section.id === activeTab) || valueSections[0];
  
  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Header Section - With Subtle Gradient */}
          <section className="bg-gradient-to-b from-light to-background py-8 relative overflow-hidden">
            {/* Subtle background decorative elements */}
            <div className="absolute inset-0 overflow-hidden opacity-10">
              <div className="absolute -top-20 -left-20 w-96 h-96 bg-crimson rounded-full blur-3xl"></div>
              <div className="absolute top-1/2 right-0 w-80 h-80 bg-gold rounded-full blur-3xl"></div>
            </div>
            
            <div className="container mx-auto px-4 max-w-6xl relative">
              <div className="max-w-3xl">
                <div className="inline-flex items-center px-3 py-1 rounded-full bg-crimson/10 text-crimson text-sm font-medium mb-2 w-fit">
                  About Us
                </div>
                <h1 className="text-2xl font-medium tracking-tight text-gray-900 mb-2">
                  Our Values
                </h1>
                <p className="text-base text-muted-foreground">
                  The core principles that guide our community and define our approach to education, research, and service.
                </p>
              </div>
            </div>
          </section>

          {/* Value Navigation Tabs */}
          <div className="sticky top-28 z-10 bg-background/95 backdrop-blur-md border-b py-2 shadow-sm">
            <div className="container mx-auto px-4 max-w-6xl">
              <nav aria-label="Value navigation" className="overflow-x-auto">
                <ul className="flex space-x-6 whitespace-nowrap px-1">
                  {valueSections.map((section) => (
                    <li key={section.id}>
                      <button 
                        onClick={() => setActiveTab(section.id)}
                        className={`text-sm font-medium flex items-center gap-1 py-2 transition-colors duration-200 ${
                          activeTab === section.id 
                            ? 'text-crimson border-b-2 border-crimson' 
                            : 'text-muted-foreground hover:text-primary'
                        }`}
                      >
                        <ChevronRight className="h-3 w-3" />
                        <span>{section.title.split(' ')[0]}</span>
                        <span className="hidden md:inline">{' ' + section.title.split(' ').slice(1).join(' ')}</span>
                      </button>
                    </li>
                  ))}
                </ul>
              </nav>
            </div>
          </div>

          {/* Main Content - Active Value */}
          <section className="py-12 bg-gradient-to-b from-background to-light/50 min-h-[60vh]">
            <div className="container mx-auto px-4 max-w-4xl">
              <div 
                className={`value-card animate-fade-in bg-white/80 rounded-lg border border-l-4 border-l-${activeSection.borderColor} shadow-sm p-6 md:p-8 transition-all duration-300 hover:shadow-md`}
              >
                <div className="flex items-center gap-4 mb-4">
                  <div className={`h-10 w-10 md:h-12 md:w-12 rounded-full bg-${activeSection.bgColor} flex items-center justify-center`}>
                    {React.cloneElement(activeSection.icon, { className: `h-5 w-5 md:h-6 md:w-6 text-${activeSection.color}` })}
                  </div>
                  <h2 className="text-xl md:text-2xl font-medium text-gray-900">{activeSection.title}</h2>
                </div>

                <p className="text-gray-700 font-medium mb-6 text-base md:text-lg">
                  {activeSection.headline}
                </p>

                {activeSection.content.map((paragraph, index) => (
                  <p key={index} className="text-muted-foreground mb-4 text-sm md:text-base">
                    {paragraph}
                  </p>
                ))}
                
                {/* Real-world example */}
                <div className={`bg-light/50 rounded-md p-4 md:p-5 border border-${activeSection.color}/20 mt-6`}>
                  <h3 className="text-sm md:text-base font-medium text-gray-900 flex items-center gap-2 mb-2">
                    <CheckCircle2 className={`h-4 w-4 text-${activeSection.color}`} />
                    In Practice
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {activeSection.example}
                  </p>
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </PageTransition>
  )
} 