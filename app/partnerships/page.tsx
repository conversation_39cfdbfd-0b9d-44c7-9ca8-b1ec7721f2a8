import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, Building, GraduationCap, Globe } from "lucide-react"
import Link from "next/link"

export const metadata = {
  title: 'Partnerships | College Name',
  description: 'Learn about our academic and industry partnerships that enhance the educational experience for our students.',
}

export default function PartnershipsPage() {
  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Header Section - With Subtle Gradient */}
          <section className="bg-gradient-to-b from-light to-background py-8 relative overflow-hidden">
            {/* Subtle background decorative elements */}
            <div className="absolute inset-0 overflow-hidden opacity-10">
              <div className="absolute -top-20 -left-20 w-96 h-96 bg-crimson rounded-full blur-3xl"></div>
              <div className="absolute top-1/2 right-0 w-80 h-80 bg-gold rounded-full blur-3xl"></div>
            </div>
            
            <div className="container mx-auto px-4 max-w-6xl relative">
              <div className="max-w-3xl">
                <div className="inline-flex items-center px-3 py-1 rounded-full bg-crimson/10 text-crimson text-sm font-medium mb-2 w-fit">
                  Collaboration
                </div>
                <h1 className="text-2xl font-medium tracking-tight text-gray-900 mb-2">
                  Partnerships
                </h1>
                <p className="text-base text-muted-foreground">
                  Our academic and industry partnerships create valuable opportunities for students, faculty, and the broader community.
                </p>
              </div>
            </div>
          </section>

          {/* Main Content */}
          <section className="py-12 bg-gradient-to-b from-background to-light/50">
            <div className="container mx-auto px-4 max-w-6xl">
              {/* Coming Soon Section */}
              <div className="bg-white/80 rounded-lg border shadow-sm p-8 text-center mb-12">
                <h2 className="text-xl font-medium mb-4 text-gray-900">Partnerships Program Under Development</h2>
                <p className="text-muted-foreground max-w-2xl mx-auto mb-6">
                  As a new institution, we're actively building partnerships with academic institutions and industry leaders. 
                  This page will soon feature our growing network of collaborators.
                </p>
                <div className="inline-flex h-10 items-center justify-center rounded-md border border-crimson/20 bg-background px-8 text-sm font-medium">
                  Coming Soon
                </div>
              </div>
              
              {/* Partnership Types */}
              <div className="grid md:grid-cols-2 gap-6 mb-12">
                <div className="bg-white/80 rounded-lg border shadow-sm p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                      <GraduationCap className="h-5 w-5 text-primary" />
                    </div>
                    <h3 className="text-lg font-medium">Academic Partnerships</h3>
                  </div>
                  <p className="text-muted-foreground mb-4">
                    We're establishing relationships with leading educational institutions locally and internationally to provide exchange opportunities, collaborative research, and shared resources.
                  </p>
                </div>
                
                <div className="bg-white/80 rounded-lg border shadow-sm p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                      <Building className="h-5 w-5 text-primary" />
                    </div>
                    <h3 className="text-lg font-medium">Industry Partnerships</h3>
                  </div>
                  <p className="text-muted-foreground mb-4">
                    Our industry partnerships will provide students with internship opportunities, mentorship programs, and practical exposure to real-world challenges and innovations.
                  </p>
                </div>
              </div>
              
              {/* Partnership Benefits */}
              <div className="bg-white/80 rounded-lg border shadow-sm p-6 mb-12">
                <h2 className="text-xl font-medium mb-6 text-gray-900">Partnership Benefits</h2>
                <div className="grid md:grid-cols-3 gap-6">
                  <div>
                    <h3 className="text-base font-medium mb-2">For Students</h3>
                    <ul className="text-sm text-muted-foreground space-y-2">
                      <li>• Access to internship opportunities</li>
                      <li>• Study abroad programs</li>
                      <li>• Industry mentorship</li>
                      <li>• Enhanced learning resources</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-base font-medium mb-2">For Faculty</h3>
                    <ul className="text-sm text-muted-foreground space-y-2">
                      <li>• Collaborative research</li>
                      <li>• Knowledge exchange</li>
                      <li>• Professional development</li>
                      <li>• Access to specialized facilities</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-base font-medium mb-2">For Partners</h3>
                    <ul className="text-sm text-muted-foreground space-y-2">
                      <li>• Access to emerging talent</li>
                      <li>• Research collaboration</li>
                      <li>• Community engagement</li>
                      <li>• Innovation opportunities</li>
                    </ul>
                  </div>
                </div>
              </div>
              
              {/* Contact Section */}
              <div className="bg-white/80 rounded-lg border shadow-sm p-6">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                  <div>
                    <h2 className="text-xl font-medium mb-2 text-gray-900">Interested in Partnering with Us?</h2>
                    <p className="text-muted-foreground">
                      We welcome conversations with potential academic and industry partners who share our vision.
                    </p>
                  </div>
                  <Link href="#">
                    <Button variant="outline" className="border-crimson/20 hover:bg-crimson/5">
                      Contact Partnerships Office <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </PageTransition>
  )
} 