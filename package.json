{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:setup": "npx tsx scripts/setup-database.ts", "db:migrate-faculty": "npx tsx scripts/migrate-faculty-data.ts", "db:reset": "prisma db push --force-reset && npm run db:setup", "test:auth": "npx tsx scripts/test-auth.ts"}, "dependencies": {"@fontsource-variable/inter": "^5.2.5", "@hookform/resolvers": "^3.9.1", "@next-auth/prisma-adapter": "^1.0.7", "@nivo/core": "^0.98.0", "@nivo/network": "^0.98.0", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@types/bcryptjs": "^2.4.6", "@types/d3": "^7.4.3", "aos": "^2.3.4", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "d3": "^7.9.0", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "framer-motion": "^12.12.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "prisma": "^6.8.2", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-intersection-observer": "^9.16.0", "react-particles": "^2.12.2", "react-resizable-panels": "^2.1.7", "react-type-animation": "^3.2.0", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tsparticles-engine": "^2.12.0", "tsparticles-slim": "^2.12.0", "vaul": "^0.9.6", "zod": "^3.25.28"}, "devDependencies": {"@types/aos": "^3.0.7", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.17", "tsx": "^4.19.4", "typescript": "^5"}}